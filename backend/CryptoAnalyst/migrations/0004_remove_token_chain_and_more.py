# Generated by Django 4.2.10 on 2025-06-26 08:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('CryptoAnalyst', '0003_initialize_market_data'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='token',
            name='chain',
        ),
        migrations.RemoveConstraint(
            model_name='asset',
            name='unique_symbol_market_type',
        ),
        migrations.RemoveConstraint(
            model_name='userfavorite',
            name='unique_user_asset_favorite',
        ),
        migrations.AlterUniqueTogether(
            name='technicalanalysis',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='analysisreport',
            name='token',
        ),
        migrations.AlterField(
            model_name='analysisreport',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_reports', to='CryptoAnalyst.asset'),
        ),
        migrations.AlterField(
            model_name='technicalanalysis',
            name='asset',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='technical_analysis', to='CryptoAnalyst.asset'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='asset',
            unique_together={('symbol', 'market_type')},
        ),
        migrations.AlterUniqueTogether(
            name='technicalanalysis',
            unique_together={('asset', 'period_start')},
        ),
        migrations.AlterUniqueTogether(
            name='userfavorite',
            unique_together={('user', 'asset')},
        ),
        migrations.RemoveField(
            model_name='technicalanalysis',
            name='token',
        ),
        migrations.DeleteModel(
            name='Token',
        ),
    ]
