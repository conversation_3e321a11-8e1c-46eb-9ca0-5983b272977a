# CoolTrade Frontend

基于 Vue 3 + TypeScript + Vite 构建的现代化前端应用。

## 🚀 技术栈

- **Vue 3.4.15** - 渐进式 JavaScript 框架
- **TypeScript 5.3.3** - JavaScript 的超集，提供类型安全
- **Vite 5.0.12** - 下一代前端构建工具
- **Vue Router 4.2.5** - Vue.js 官方路由管理器
- **Pinia 2.1.7** - Vue 的状态管理库
- **TailwindCSS 3.4.1** - 实用优先的 CSS 框架
- **Element Plus 2.9.9** - Vue 3 组件库
- **RemixIcon 4.2.0** - 开源图标库

## 📦 安装依赖

```bash
npm install
```

## 🛠️ 开发

```bash
# 启动开发服务器
npm run dev

# 访问 http://localhost:5000
```

## 🏗️ 构建

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📁 项目结构

```
frontend-new/
├── src/
│   ├── views/          # 页面组件
│   ├── router/         # 路由配置
│   ├── App.vue         # 根组件
│   ├── main.ts         # 应用入口
│   └── style.css       # 全局样式
├── index.html          # HTML 模板
├── vite.config.ts      # Vite 配置
├── tailwind.config.js  # TailwindCSS 配置
└── package.json        # 项目配置
```

## ✨ 特性

- ⚡ **快速开发** - Vite 提供极速的热重载
- 🔷 **类型安全** - TypeScript 提供完整的类型检查
- 🎨 **现代 UI** - TailwindCSS + Element Plus
- 📱 **响应式设计** - 完美适配各种设备
- 🧭 **路由管理** - Vue Router 4 提供强大的路由功能
- 🔧 **开发体验** - 完整的开发工具链

## 🌟 开始使用

1. 克隆项目
2. 安装依赖：`npm install`
3. 启动开发：`npm run dev`
4. 打开浏览器访问：`http://localhost:5000`

## 📝 许可证

MIT License
