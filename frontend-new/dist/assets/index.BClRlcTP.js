var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertyNames,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(e,t)=>(t=Symbol[e])?t:Symbol.for("Symbol."+e),l=(t,r,a)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a,c=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&l(e,r,t[r]);if(n)for(var r of n(t))s.call(t,r)&&l(e,r,t[r]);return e},d=(e,a)=>t(e,r(a)),u=(e,t,r)=>new Promise(((a,n)=>{var o=e=>{try{i(r.next(e))}catch(t){n(t)}},s=e=>{try{i(r.throw(e))}catch(t){n(t)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,s);i((r=r.apply(e,t)).next())})),p=function(e,t){this[0]=e,this[1]=t},m=(e,t,r)=>{var a=(e,t,n,o)=>{try{var s=r[e](t),i=(t=s.value)instanceof p,l=s.done;Promise.resolve(i?t[0]:t).then((r=>i?a("return"===e?e:"next",t[1]?{done:r.done,value:r.value}:r,n,o):n({value:r,done:l}))).catch((e=>a("throw",e,n,o)))}catch(c){o(c)}},n=e=>o[e]=t=>new Promise(((r,n)=>a(e,t,r,n))),o={};return r=r.apply(e,t),o[i("asyncIterator")]=()=>o,n("next"),n("throw"),n("return"),o},f=e=>{var t,r=e[i("asyncIterator")],a=!1,n={};return null==r?(r=e[i("iterator")](),t=e=>n[e]=t=>r[e](t)):(r=r.call(e),t=e=>n[e]=t=>{if(a){if(a=!1,"throw"===e)throw t;return t}return a=!0,{done:!1,value:new p(new Promise((a=>{var n=r[e](t);n instanceof Object||(e=>{throw TypeError(e)})("Object expected"),a(n)})),1)}}),n[i("iterator")]=()=>n,t("next"),"throw"in r?t("throw"):n.throw=e=>{throw e},"return"in r&&t("return"),n};import{ao as g,r as _,s as v,c as h,j as y,l as b,x as w,af as x,O as k,g as S,i as E,h as R,S as C,P as O,$ as T,y as P,aw as A,A as I,M as j,z as N,J as F,D as L,a6 as D,u as U,B as M,G as B,I as z,ak as q,H as W,L as V,ag as $,ax as J,V as K,ay as H,az as G,at as Y,aA as X}from"./vendor.DXRd09KZ.js";import{E as Q,a as Z,i as ee}from"./ui.DtXW3Hkc.js";var te,re,ae=(te={"assets/index.BClRlcTP.js"(e){!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();
/*!
      * shared v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */
const t="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),a=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),n=e=>"number"==typeof e&&isFinite(e),o=e=>"[object RegExp]"===fe(e),s=e=>ge(e)&&0===Object.keys(e).length,l=Object.assign,te=Object.create,re=(e=null)=>te(e);let ae;const ne=()=>ae||(ae="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:re());function oe(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const se=Object.prototype.hasOwnProperty;function ie(e,t){return se.call(e,t)}const le=Array.isArray,ce=e=>"function"==typeof e,de=e=>"string"==typeof e,ue=e=>"boolean"==typeof e,pe=e=>null!==e&&"object"==typeof e,me=Object.prototype.toString,fe=e=>me.call(e),ge=e=>"[object Object]"===fe(e);function _e(e,t){}const ve=e=>!pe(e)||le(e);function he(e,t){if(ve(e)||ve(t))throw new Error("Invalid value");const r=[{src:e,des:t}];for(;r.length;){const{src:e,des:t}=r.pop();Object.keys(e).forEach((a=>{"__proto__"!==a&&(pe(e[a])&&!pe(t[a])&&(t[a]=Array.isArray(e[a])?[]:re()),ve(t[a])||ve(e[a])?t[a]=e[a]:r.push({src:e[a],des:t[a]}))}))}}
/*!
      * message-compiler v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */function ye(e,t,r={}){const{domain:a,messages:n,args:o}=r,s=new SyntaxError(String(e));return s.code=e,s.domain=a,s}
/*!
      * core-base v12.0.0-alpha.2
      * (c) 2016-present kazuya kawaguchi and contributors
      * Released under the MIT License.
      */const be=["t","type"];function we(e){return function(e,t,r){for(let a=0;a<t.length;a++){const r=t[a];if(ie(e,r)&&null!=e[r])return e[r]}return r}(e,be)}function xe(e){return pe(e)&&0===we(e)&&(ie(e,"b")||ie(e,"body"))}let ke=null;const Se=Ee("function:translate");function Ee(e){return t=>ke&&ke.emit(e,t)}const Re=17,Ce=18,Oe=19,Te=21,Pe=22,Ae=23;function Ie(e){return ye(e,0,void 0)}function je(e,t){return null!=t.locale?Fe(t.locale):Fe(e.locale)}let Ne;function Fe(e){if(de(e))return e;if(ce(e)){if(e.resolvedOnce&&null!=Ne)return Ne;if("Function"===e.constructor.name){const r=e();if(pe(t=r)&&ce(t.then)&&ce(t.catch))throw Ie(Te);return Ne=r}throw Ie(Pe)}throw Ie(Ae);var t}function Le(e,t,r){return[...new Set([r,...le(t)?t:pe(t)?Object.keys(t):de(t)?[t]:[r]])]}function De(e,t,r){let a=!0;for(let n=0;n<t.length&&ue(a);n++){const o=t[n];de(o)&&(a=Ue(e,t[n],r))}return a}function Ue(e,t,r){let a;const n=t.split("-");do{a=Me(e,n.join("-"),r),n.splice(-1,1)}while(n.length&&!0===a);return a}function Me(e,t,r){let a=!1;if(!e.includes(t)&&(a=!0,t)){a="!"!==t[t.length-1];const n=t.replace(/!/g,"");e.push(n),(le(r)||ge(r))&&r[n]&&(a=r[n])}return a}function Be(e,t){return pe(e)?e[t]:null}const ze="en-US",qe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let We=null;const Ve=()=>We;let $e=null;const Je=e=>{$e=e};let Ke=0;function He(e={}){const t=ce(e.onWarn)?e.onWarn:_e,r=de(e.version)?e.version:"12.0.0-alpha.2",a=de(e.locale)||ce(e.locale)?e.locale:ze,n=ce(a)?ze:a,s=le(e.fallbackLocale)||ge(e.fallbackLocale)||de(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,i=ge(e.messages)?e.messages:Ge(n),c=ge(e.datetimeFormats)?e.datetimeFormats:Ge(n),d=ge(e.numberFormats)?e.numberFormats:Ge(n),u=l(re(),e.modifiers,{upper:(e,t)=>"text"===t&&de(e)?e.toUpperCase():"vnode"===t&&pe(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&de(e)?e.toLowerCase():"vnode"===t&&pe(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&de(e)?qe(e):"vnode"===t&&pe(e)&&"__v_isVNode"in e?qe(e.children):e}),p=e.pluralRules||re(),m=ce(e.missing)?e.missing:null,f=!ue(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,g=!ue(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,_=!!e.fallbackFormat,v=!!e.unresolving,h=ce(e.postTranslation)?e.postTranslation:null,y=ge(e.processor)?e.processor:null,b=!ue(e.warnHtmlMessage)||e.warnHtmlMessage,w=!!e.escapeParameter,x=ce(e.messageCompiler)?e.messageCompiler:void 0,k=ce(e.messageResolver)?e.messageResolver:Be,S=ce(e.localeFallbacker)?e.localeFallbacker:Le,E=pe(e.fallbackContext)?e.fallbackContext:void 0,R=e,C=pe(R.__datetimeFormatters)?R.__datetimeFormatters:new Map,O=pe(R.__numberFormatters)?R.__numberFormatters:new Map,T=pe(R.__meta)?R.__meta:{};Ke++;const P={version:r,cid:Ke,locale:a,fallbackLocale:s,messages:i,modifiers:u,pluralRules:p,missing:m,missingWarn:f,fallbackWarn:g,fallbackFormat:_,unresolving:v,postTranslation:h,processor:y,warnHtmlMessage:b,escapeParameter:w,messageCompiler:x,messageResolver:k,localeFallbacker:S,fallbackContext:E,onWarn:t,__meta:T};return P.datetimeFormats=c,P.numberFormats=d,P.__datetimeFormatters=C,P.__numberFormatters=O,__INTLIFY_PROD_DEVTOOLS__&&function(e,t,r){ke&&ke.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:r})}(P,r,T),P}const Ge=e=>({[e]:re()});function Ye(e,t,r,a,n){const{missing:o,onWarn:s}=e;if(null!==o){const a=o(e,r,t,n);return de(a)?a:t}return t}function Xe(e,t,r){e.__localeChainCache=new Map,e.localeFallbacker(e,r,t)}function Qe(e,t){const r=t.indexOf(e);if(-1===r)return!1;for(let o=r+1;o<t.length;o++)if(a=e,n=t[o],a!==n&&a.split("-")[0]===n.split("-")[0])return!0;var a,n;return!1}function Ze(e,...t){const{datetimeFormats:r,unresolving:a,fallbackLocale:n,onWarn:o,localeFallbacker:i}=e,{__datetimeFormatters:c}=e,[d,u,p,m]=tt(...t);ue(p.missingWarn)?p.missingWarn:e.missingWarn,ue(p.fallbackWarn)?p.fallbackWarn:e.fallbackWarn;const f=!!p.part,g=je(e,p),_=i(e,n,g);if(!de(d)||""===d)return new Intl.DateTimeFormat(g,m).format(u);let v,h={},y=null;for(let s=0;s<_.length&&(v=_[s],h=r[v]||{},y=h[d],!ge(y));s++)Ye(e,d,v,0,"datetime format");if(!ge(y)||!de(v))return a?-1:d;let b=`${v}__${d}`;s(m)||(b=`${b}__${JSON.stringify(m)}`);let w=c.get(b);return w||(w=new Intl.DateTimeFormat(v,l({},y,m)),c.set(b,w)),f?w.formatToParts(u):w.format(u)}const et=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function tt(...e){const[t,r,a,o]=e,s=re();let i,l=re();if(de(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Ie(Oe);const r=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(r);try{i.toISOString()}catch(c){throw Ie(Oe)}}else if("[object Date]"===fe(t)){if(isNaN(t.getTime()))throw Ie(Ce);i=t}else{if(!n(t))throw Ie(Re);i=t}return de(r)?s.key=r:ge(r)&&Object.keys(r).forEach((e=>{et.includes(e)?l[e]=r[e]:s[e]=r[e]})),de(a)?s.locale=a:ge(a)&&(l=a),ge(o)&&(l=o),[s.key||"",i,s,l]}function rt(e,t,r){const a=e;for(const n in r){const e=`${t}__${n}`;a.__datetimeFormatters.has(e)&&a.__datetimeFormatters.delete(e)}}function at(e,...t){const{numberFormats:r,unresolving:a,fallbackLocale:n,onWarn:o,localeFallbacker:i}=e,{__numberFormatters:c}=e,[d,u,p,m]=ot(...t);ue(p.missingWarn)?p.missingWarn:e.missingWarn,ue(p.fallbackWarn)?p.fallbackWarn:e.fallbackWarn;const f=!!p.part,g=je(e,p),_=i(e,n,g);if(!de(d)||""===d)return new Intl.NumberFormat(g,m).format(u);let v,h={},y=null;for(let s=0;s<_.length&&(v=_[s],h=r[v]||{},y=h[d],!ge(y));s++)Ye(e,d,v,0,"number format");if(!ge(y)||!de(v))return a?-1:d;let b=`${v}__${d}`;s(m)||(b=`${b}__${JSON.stringify(m)}`);let w=c.get(b);return w||(w=new Intl.NumberFormat(v,l({},y,m)),c.set(b,w)),f?w.formatToParts(u):w.format(u)}const nt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ot(...e){const[t,r,a,o]=e,s=re();let i=re();if(!n(t))throw Ie(Re);const l=t;return de(r)?s.key=r:ge(r)&&Object.keys(r).forEach((e=>{nt.includes(e)?i[e]=r[e]:s[e]=r[e]})),de(a)?s.locale=a:ge(a)&&(i=a),ge(o)&&(i=o),[s.key||"",l,s,i]}function st(e,t,r){const a=e;for(const n in r){const e=`${t}__${n}`;a.__numberFormatters.has(e)&&a.__numberFormatters.delete(e)}}const it=e=>e,lt=e=>"",ct=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,r,a)=>0===a?e+r:e+t+r),"")}(e),dt=e=>null==e?"":le(e)||ge(e)&&e.toString===me?JSON.stringify(e,null,2):String(e);function ut(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function pt(e={}){const t=e.locale,r=function(e){const t=n(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(n(e.named.count)||n(e.named.n))?n(e.named.count)?e.named.count:n(e.named.n)?e.named.n:t:t}(e),a=pe(e.pluralRules)&&de(t)&&ce(e.pluralRules[t])?e.pluralRules[t]:ut,o=pe(e.pluralRules)&&de(t)&&ce(e.pluralRules[t])?ut:void 0,s=e.list||[],i=e.named||re();function c(t,r){const a=ce(e.messages)?e.messages(t,!!r):!!pe(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):lt)}n(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(r,i);const d=ge(e.processor)&&ce(e.processor.normalize)?e.processor.normalize:ct,u=ge(e.processor)&&ce(e.processor.interpolate)?e.processor.interpolate:dt,p={list:e=>s[e],named:e=>i[e],plural:e=>e[a(r,e.length,o)],linked:(t,...r)=>{const[a,n]=r;let o="text",s="";1===r.length?pe(a)?(s=a.modifier||s,o=a.type||o):de(a)&&(s=a||s):2===r.length&&(de(a)&&(s=a||s),de(n)&&(o=n||o));const i=c(t,!0)(p),l="vnode"===o&&le(i)&&s?i[0]:i;return s?(d=s,e.modifiers?e.modifiers[d]:it)(l,o):l;var d},message:c,type:ge(e.processor)&&de(e.processor.type)?e.processor.type:"text",interpolate:u,normalize:d,values:l(re(),s,i)};return p}const mt=()=>"",ft=e=>ce(e);function gt(e,...t){const{fallbackFormat:r,postTranslation:a,unresolving:o,messageCompiler:s,fallbackLocale:i,messages:c}=e,[d,u]=ht(...t),p=(ue(u.missingWarn)?u.missingWarn:e.missingWarn,ue(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,ue(u.escapeParameter)?u.escapeParameter:e.escapeParameter),m=!!u.resolvedMessage,f=de(u.default)||ue(u.default)?ue(u.default)?s?d:()=>d:u.default:r?s?d:()=>d:null,g=r||null!=f&&(de(f)||ce(f)),_=je(e,u);p&&function(e){le(e.list)?e.list=e.list.map((e=>de(e)?oe(e):e)):pe(e.named)&&Object.keys(e.named).forEach((t=>{de(e.named[t])&&(e.named[t]=oe(e.named[t]))}))}(u);let[v,h,y]=m?[d,_,c[_]||re()]:_t(e,d,_,i),b=v,w=d;if(m||de(b)||xe(b)||ft(b)||g&&(b=f,w=b),!(m||(de(b)||xe(b)||ft(b))&&de(h)))return o?-1:d;let x=!1;const k=ft(b)?b:vt(e,d,h,b,w,(()=>{x=!0}));if(x)return b;const S=function(e,t,r,a){const{modifiers:o,pluralRules:s,messageResolver:i,fallbackLocale:l,fallbackWarn:c,missingWarn:d,fallbackContext:u}=e,p=(a,n)=>{let o=i(r,a);if(null==o&&(u||n)){const[,,r]=_t(u||e,a,t,l);o=i(r,a)}if(de(o)||xe(o)){let r=!1;const n=vt(e,a,t,o,a,(()=>{r=!0}));return r?mt:n}return ft(o)?o:mt},m={locale:t,modifiers:o,pluralRules:s,messages:p};return e.processor&&(m.processor=e.processor),a.list&&(m.list=a.list),a.named&&(m.named=a.named),n(a.plural)&&(m.pluralIndex=a.plural),m}(e,h,y,u),E=function(e,t,r){const a=t(r);return a}(0,k,pt(S)),R=a?a(E,d):E;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:de(d)?d:ft(b)?b.key:"",locale:h||(ft(b)?b.locale:""),format:de(b)?b:ft(b)?b.source:"",message:R};t.meta=l({},e.__meta,Ve()||{}),Se(t)}return R}function _t(e,t,r,a,n,o){const{messages:s,onWarn:i,messageResolver:l,localeFallbacker:c}=e,d=c(e,a,r);let u,p=re(),m=null;for(let f=0;f<d.length&&(u=d[f],p=s[u]||re(),null===(m=l(p,t))&&(m=p[t]),!(de(m)||xe(m)||ft(m)));f++)if(!Qe(u,d)){const r=Ye(e,t,u,0,"translate");r!==t&&(m=r)}return[m,u,p]}function vt(e,t,r,n,o,s){const{messageCompiler:i,warnHtmlMessage:l}=e;if(ft(n)){const e=n;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==i){const e=()=>n;return e.locale=r,e.key=t,e}const c=i(n,function(e,t,r,n,o,s){return{locale:t,key:r,warnHtmlMessage:o,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,t,r)=>a({l:e,k:t,s:r}))(t,r,e)}}(0,r,o,0,l,s));return c.locale=r,c.key=t,c.source=n,c}function ht(...e){const[t,r,a]=e,o=re();if(!(de(t)||n(t)||ft(t)||xe(t)))throw Ie(Re);const i=n(t)?String(t):(ft(t),t);return n(r)?o.plural=r:de(r)?o.default=r:ge(r)&&!s(r)?o.named=r:le(r)&&(o.list=r),n(a)?o.plural=a:de(a)?o.default=a:ge(a)&&l(o,a),[i,o]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ne().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ne().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const yt=24,bt=25,wt=26,xt=27,kt=31,St=32;function Et(e,...t){return ye(e,0,void 0)}const Rt=r("__translateVNode"),Ct=r("__datetimeParts"),Ot=r("__numberParts"),Tt=r("__setPluralRules"),Pt=r("__injectWithOption"),At=r("__dispose");function It(e){if(!pe(e))return e;for(const t in e)if(ie(e,t))if(t.includes(".")){const r=t.split("."),a=r.length-1;let n=e,o=!1;for(let e=0;e<a;e++){if("__proto__"===r[e])throw new Error(`unsafe key: ${r[e]}`);if(r[e]in n||(n[r[e]]=re()),!pe(n[r[e]])){o=!0;break}n=n[r[e]]}o||(n[r[a]]=e[t],delete e[t]),pe(n[r[a]])&&It(n[r[a]])}else pe(e[t])&&It(e[t]);return e}function jt(e,t){const{messages:r,__i18n:a,messageResolver:n,flatJson:o}=t,s=ge(r)?r:le(a)?re():{[e]:re()};if(le(a)&&a.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:r}=e;t?(s[t]=s[t]||re(),he(r,s[t])):he(r,s)}else de(e)&&he(JSON.parse(e),s)})),null==n&&o)for(const i in s)ie(s,i)&&It(s[i]);return s}function Nt(e){return e.type}function Ft(e){return O(T,null,e,0)}const Lt=()=>[],Dt=()=>!1;let Ut=0;function Mt(e){return(t,r,a,n)=>e(r,a,S()||void 0,n)}function Bt(e={}){const{__root:r,__injectWithOption:a}=e,s=void 0===r,i=e.flatJson,c=t?_:v;let d=!ue(e.inheritLocale)||e.inheritLocale;const u=c(r&&d?r.locale.value:de(e.locale)?e.locale:ze),p=c(r&&d?r.fallbackLocale.value:de(e.fallbackLocale)||le(e.fallbackLocale)||ge(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:u.value),m=c(jt(u.value,e)),f=c(ge(e.datetimeFormats)?e.datetimeFormats:{[u.value]:{}}),g=c(ge(e.numberFormats)?e.numberFormats:{[u.value]:{}});let b=r?r.missingWarn:!ue(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,w=r?r.fallbackWarn:!ue(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,x=r?r.fallbackRoot:!ue(e.fallbackRoot)||e.fallbackRoot,k=!!e.fallbackFormat,S=ce(e.missing)?e.missing:null,E=ce(e.missing)?Mt(e.missing):null,R=ce(e.postTranslation)?e.postTranslation:null,C=r?r.warnHtmlMessage:!ue(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter;const T=r?r.modifiers:ge(e.modifiers)?e.modifiers:{};let P,A=e.pluralRules||r&&r.pluralRules;P=(()=>{s&&Je(null);const t={version:"12.0.0-alpha.2",locale:u.value,fallbackLocale:p.value,messages:m.value,modifiers:T,pluralRules:A,missing:null===E?void 0:E,missingWarn:b,fallbackWarn:w,fallbackFormat:k,unresolving:!0,postTranslation:null===R?void 0:R,warnHtmlMessage:C,escapeParameter:O,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=f.value,t.numberFormats=g.value,t.__datetimeFormatters=ge(P)?P.__datetimeFormatters:void 0,t.__numberFormatters=ge(P)?P.__numberFormatters:void 0;const r=He(t);return s&&Je(r),r})(),Xe(P,u.value,p.value);const I=h({get:()=>u.value,set:e=>{P.locale=e,u.value=e}}),j=h({get:()=>p.value,set:e=>{P.fallbackLocale=e,p.value=e,Xe(P,u.value,e)}}),N=h((()=>m.value)),F=h((()=>Object.keys(m.value).sort())),L=h((()=>f.value)),D=h((()=>g.value)),U=(e,t,a,o,i,l)=>{let c;u.value,p.value,m.value,f.value,g.value;try{__INTLIFY_PROD_DEVTOOLS__,s||(P.fallbackContext=r?$e:void 0),c=e(P)}finally{__INTLIFY_PROD_DEVTOOLS__,s||(P.fallbackContext=void 0)}if("translate exists"!==a&&n(c)&&-1===c||"translate exists"===a&&!c){const[e,a]=t();return r&&x?o(r):i(e)}if(l(c))return c;throw Et(yt)};function M(...e){return U((t=>Reflect.apply(gt,null,[t,...e])),(()=>ht(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>de(e)))}const B={normalize:function(e){return e.map((e=>de(e)||n(e)||ue(e)?Ft(String(e)):e))},interpolate:e=>e,type:"vnode"};function z(e){let t=null;const r=function(e,t,r){const a=de(r)?r:ze,n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let o=n.__localeChainCache.get(a);if(!o){o=[];let e=[r];for(;le(e);)e=De(o,e,t);const s=le(t)||!ge(t)?t:t.default?t.default:null;e=de(s)?[s]:s,le(e)&&De(o,e,!1),n.__localeChainCache.set(a,o)}return o}(P,p.value,u.value);for(let a=0;a<r.length;a++){const n=m.value[r[a]]||{},o=P.messageResolver(n,e);if(null!=o){t=o;break}}return t}function q(e){return m.value[e]||{}}Ut++,r&&t&&(y(r.locale,(e=>{d&&(u.value=e,P.locale=e,Xe(P,u.value,p.value))})),y(r.fallbackLocale,(e=>{d&&(p.value=e,P.fallbackLocale=e,Xe(P,u.value,p.value))})));const W={id:Ut,locale:I,fallbackLocale:j,get inheritLocale(){return d},set inheritLocale(e){d=e,e&&r&&(u.value=r.locale.value,p.value=r.fallbackLocale.value,Xe(P,u.value,p.value))},availableLocales:F,messages:N,get modifiers(){return T},get pluralRules(){return A||{}},get isGlobal(){return s},get missingWarn(){return b},set missingWarn(e){b=e,P.missingWarn=b},get fallbackWarn(){return w},set fallbackWarn(e){w=e,P.fallbackWarn=w},get fallbackRoot(){return x},set fallbackRoot(e){x=e},get fallbackFormat(){return k},set fallbackFormat(e){k=e,P.fallbackFormat=k},get warnHtmlMessage(){return C},set warnHtmlMessage(e){C=e,P.warnHtmlMessage=e},get escapeParameter(){return O},set escapeParameter(e){O=e,P.escapeParameter=e},t:M,getLocaleMessage:q,setLocaleMessage:function(e,t){if(i){const r={[e]:t};for(const e in r)ie(r,e)&&It(r[e]);t=r[e]}m.value[e]=t,P.messages=m.value},mergeLocaleMessage:function(e,t){m.value[e]=m.value[e]||{};const r={[e]:t};if(i)for(const a in r)ie(r,a)&&It(r[a]);he(t=r[e],m.value[e]),P.messages=m.value},getPostTranslationHandler:function(){return ce(R)?R:null},setPostTranslationHandler:function(e){R=e,P.postTranslation=e},getMissingHandler:function(){return S},setMissingHandler:function(e){null!==e&&(E=Mt(e)),S=e,P.missing=E},[Tt]:function(e){A=e,P.pluralRules=A}};return W.datetimeFormats=L,W.numberFormats=D,W.rt=function(...e){const[t,r,a]=e;if(a&&!pe(a))throw Et(bt);return M(t,r,l({resolvedMessage:!0},a||{}))},W.te=function(e,t){return U((()=>{if(!e)return!1;const r=q(de(t)?t:u.value),a=P.messageResolver(r,e);return xe(a)||ft(a)||de(a)}),(()=>[e]),"translate exists",(r=>Reflect.apply(r.te,r,[e,t])),Dt,(e=>ue(e)))},W.tm=function(e){const t=z(e);return null!=t?t:r&&r.tm(e)||{}},W.d=function(...e){return U((t=>Reflect.apply(Ze,null,[t,...e])),(()=>tt(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>de(e)))},W.n=function(...e){return U((t=>Reflect.apply(at,null,[t,...e])),(()=>ot(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>de(e)))},W.getDateTimeFormat=function(e){return f.value[e]||{}},W.setDateTimeFormat=function(e,t){f.value[e]=t,P.datetimeFormats=f.value,rt(P,e,t)},W.mergeDateTimeFormat=function(e,t){f.value[e]=l(f.value[e]||{},t),P.datetimeFormats=f.value,rt(P,e,t)},W.getNumberFormat=function(e){return g.value[e]||{}},W.setNumberFormat=function(e,t){g.value[e]=t,P.numberFormats=g.value,st(P,e,t)},W.mergeNumberFormat=function(e,t){g.value[e]=l(g.value[e]||{},t),P.numberFormats=g.value,st(P,e,t)},W[Pt]=a,W[Rt]=function(...e){return U((t=>{let r;const a=t;try{a.processor=B,r=Reflect.apply(gt,null,[a,...e])}finally{a.processor=null}return r}),(()=>ht(...e)),"translate",(t=>t[Rt](...e)),(e=>[Ft(e)]),(e=>le(e)))},W[Ct]=function(...e){return U((t=>Reflect.apply(Ze,null,[t,...e])),(()=>tt(...e)),"datetime format",(t=>t[Ct](...e)),Lt,(e=>de(e)||le(e)))},W[Ot]=function(...e){return U((t=>Reflect.apply(at,null,[t,...e])),(()=>ot(...e)),"number format",(t=>t[Ot](...e)),Lt,(e=>de(e)||le(e)))},W}const zt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function qt(){return k}function Wt(e,t,r,a){const{slots:n,attrs:o}=t;return()=>{const t={part:!0};let s=re();e.locale&&(t.locale=e.locale),de(e.format)?t.key=e.format:pe(e.format)&&(de(e.format.key)&&(t.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>r.includes(a)?l(re(),t,{[a]:e.format[a]}):t),re()));const i=a(e.value,t,s);let c=[t.key];le(i)?c=i.map(((e,t)=>{const r=n[e.type],a=r?r({[e.type]:e.value,index:t,parts:i}):[e.value];var o;return le(o=a)&&!de(o[0])&&(a[0].key=`${e.type}-${t}`),a})):de(i)&&(c=[i]);const d=l(re(),o),u=de(e.tag)||pe(e.tag)?e.tag:qt();return x(u,d,c)}}const Vt=w({name:"i18n-d",props:l({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},zt),setup(e,t){const r=e.i18n||Ht({useScope:e.scope,__useComponent:!0});return Wt(e,t,et,((...e)=>r[Ct](...e)))}}),$t=w({name:"i18n-n",props:l({value:{type:Number,required:!0},format:{type:[String,Object]}},zt),setup(e,t){const r=e.i18n||Ht({useScope:e.scope,__useComponent:!0});return Wt(e,t,nt,((...e)=>r[Ot](...e)))}}),Jt=w({name:"i18n-t",props:l({},{keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>n(e)||!isNaN(e)}},zt),setup(e,t){const{slots:r,attrs:a}=t,n=e.i18n||Ht({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(r).filter((e=>"_"!==e)),s=re();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=de(e.plural)?+e.plural:e.plural);const i=function({slots:e},t){return 1===t.length&&"default"===t[0]?(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===k?t.children:[t]]),[]):t.reduce(((t,r)=>{const a=e[r];return a&&(t[r]=a()),t}),re())}(t,o),c=n[Rt](e.keypath,i,s),d=l(re(),a),u=de(e.tag)||pe(e.tag)?e.tag:qt();return x(u,d,c)}}}),Kt=r("global-vue-i18n");function Ht(e={}){const t=S();if(null==t)throw Et(wt);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Et(xt);const r=function(e){const t=E(e.isCE?Kt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Et(e.isCE?kt:St);return t}(t),a=function(e){return e.global}(r),n=Nt(t),o=function(e,t){return s(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,n);if("global"===o)return function(e,t,r){let a=pe(t.messages)?t.messages:re();"__i18nGlobal"in r&&(a=jt(e.locale.value,{messages:a,__i18n:r.__i18nGlobal}));const n=Object.keys(a);if(n.length&&n.forEach((t=>{e.mergeLocaleMessage(t,a[t])})),pe(t.datetimeFormats)){const r=Object.keys(t.datetimeFormats);r.length&&r.forEach((r=>{e.mergeDateTimeFormat(r,t.datetimeFormats[r])}))}if(pe(t.numberFormats)){const r=Object.keys(t.numberFormats);r.length&&r.forEach((r=>{e.mergeNumberFormat(r,t.numberFormats[r])}))}}(a,e,n),a;if("parent"===o){let n=function(e,t,r=!1){let a=null;const n=t.root;let o=function(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}(t,r);for(;null!=o&&(a=e.__getInstance(o),null==a)&&n!==o;)o=o.parent;return a}(r,t,e.__useComponent);return null==n&&(n=a),n}const i=r;let c=i.__getInstance(t);if(null==c){const r=l({},e);"__i18n"in n&&(r.__i18n=n.__i18n),a&&(r.__root=a),c=Bt(r),i.__composerExtend&&(c[At]=i.__composerExtend(c)),function(e,t,r){R((()=>{}),t),C((()=>{const a=r;e.__deleteInstance(t);const n=a[At];n&&(n(),delete a[At])}),t)}(i,t,c),i.__setInstance(t,c)}return c}const Gt=["locale","fallbackLocale","availableLocales"],Yt=["t","rt","d","n","tm","te"];if("boolean"!=typeof __VUE_I18N_FULL_INSTALL__&&(ne().__VUE_I18N_FULL_INSTALL__=!0),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ne().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ne().__INTLIFY_PROD_DEVTOOLS__=!1),__INTLIFY_PROD_DEVTOOLS__){const e=ne();e.__INTLIFY__=!0,Xt=e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__,ke=Xt}var Xt;const Qt={common:{app_name:"Cooltrade",loading:"加载中...",refresh:"刷新",refreshing:"刷新中...",save:"保存",cancel:"取消",confirm:"确认",back:"返回",success:"成功",error:"错误",warning:"警告",info:"信息",yes:"是",no:"否",ok:"确定",language:"语言",sending:"发送中...",registering:"注册中...",submitting:"提交中...",retry:"重试",privacy_policy:"隐私政策",about_us:"关于我们",no_data:"暂无数据",load_data:"加载数据",popular_tokens:"热门代币",popular_stocks:"热门股票",quick_switch:"快速切换",search:"搜索",coming_soon:"开发中",my_favorites:"我的收藏",items:"项",search_crypto_placeholder:"搜索加密货币代码或名称...",search_stock_placeholder:"搜索股票代码或名称..."},market:{crypto:"加密货币",stock:"美股",china:"A股"},auth:{login:"登录",register:"注册",logout:"退出登录",email:"邮箱",password:"密码",confirm_password:"确认密码",verification_code:"验证码",send_code:"发送验证码",forgot_password:"忘记密码",reset_password:"重置密码",change_password:"修改密码",current_password:"当前密码",new_password:"新密码",confirm_new_password:"确认密码",invitation_code:"邀请码",login_success:"登录成功",register_success:"注册成功",logout_success:"退出成功",password_changed:"密码已修改",password_reset:"密码已重置",code_sent:"验证码已发送",no_account:"还没有账号？",register_now:"立即注册",have_account:"已有账号？",login_now:"立即登录",email_placeholder:"请输入邮箱",password_placeholder:"请输入密码",verification_code_placeholder:"请输入验证码",invitation_code_placeholder:"请输入邀请码",retry_in_seconds:"{seconds}秒后重试",password_requirements:"密码至少6位，包含字母和数字",password_changed_success:"密码修改成功",login_required_for_password_change:"请先登录后再修改密码",go_to_login:"去登录",enter_current_and_new_password:"请输入当前密码和新密码",new_password_placeholder:"请输入新密码",confirm_new_password_placeholder:"请再次输入新密码",resetting:"重置中...",reset_success:"密码重置成功",reset_success_message:"您的密码已成功重置，请使用新密码登录。",back_to_login:"返回登录"},profile:{profile:"个人资料",username:"用户名",email:"邮箱",created_at:"创建时间",updated_at:"更新时间",registration_time:"注册时间",language_preference:"语言偏好",generate_invitation:"生成邀请码",invitation_codes:"邀请码列表",language_settings:"语言设置",select_language:"选择语言",chinese:"简体中文",english:"英文",japanese:"日语",korean:"韩语"},analysis:{technical_analysis:"技术分析",market_data:"市场数据",trend_analysis:"趋势分析",indicators:"指标",trading_advice:"交易建议",risk_assessment:"风险评估",current_price:"当前价格",last_update:"最后更新",refresh_data:"刷新数据",refreshing:"正在刷新...",up_probability:"上涨概率",sideways_probability:"横盘概率",down_probability:"下跌概率",trend_summary:"趋势总结",action:"操作建议",reason:"建议原因",entry_price:"入场价格",stop_loss:"止损价格",take_profit:"止盈价格",risk_level:"风险等级",risk_score:"风险分数",risk_details:"风险详情",high_risk:"高风险",medium_risk:"中等风险",low_risk:"低风险",market_report:"{symbol}市场分析报告",snapshot_price:"快照价格",share_to_twitter:"分享到推特",save_image:"保存图片",uptrend:"上涨趋势",sideways:"横盘整理",downtrend:"下跌趋势",market_trend_analysis:"市场趋势分析",technical_indicators:"技术指标",recommended_action:"建议操作",risk_factors:"风险因素",refreshing_data:"正在刷新数据",refreshing_data_ellipsis:"正在刷新数据...",calculating_indicators:"正在获取市场数据并进行技术指标计算...",analyzing_trends:"正在进行趋势分析和概率评估...",generating_advice:"正在生成交易建议和风险评估...",finalizing_data:"最终数据整合中，即将完成...",force_refresh:"强制刷新",minute_ago:"{n}分钟前",hour_ago:"{n}小时前",day_ago:"{n}天前",preparing_analysis_report:"正在准备分析报告...",generating_new_report:"正在生成新的分析报告，请耐心等待...",please_wait:"请耐心等待，这可能需要一些时间",timeout_error:"请求超时，服务器正在处理中，请稍后重试",refresh_report:"刷新报告",refresh_report_too_soon:"报告未超过12小时，暂不可刷新"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"布林带",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"资金费率",exchange_netflow:"交易所净流入",nupl:"NUPL",mayer_multiple:"梅耶倍数"},errors:{network_error:"网络错误",server_error:"服务器错误",not_found:"未找到",unauthorized:"未授权",forbidden:"禁止访问",validation_error:"验证错误",unknown_error:"未知错误",token_expired:"登录已过期，请重新登录",invalid_credentials:"邮箱或密码错误",invalid_code:"验证码无效或已过期",passwords_not_match:"两次输入的密码不一致",email_already_registered:"该邮箱已被注册",email_not_registered:"该邮箱未注册",invalid_invitation_code:"邀请码无效或已使用",weak_password:"密码强度不足",fill_all_fields:"请填写所有必填字段",login_failed_no_token:"登录失败：未获取到 token",login_failed_server_error:"登录失败：服务器响应格式错误",login_failed_check_input:"登录失败，请检查输入",too_many_attempts:"登录尝试次数过多，请稍后再试",network_timeout:"网络连接超时，请检查网络",login_failed_try_later:"登录失败，请稍后重试",email_required:"请输入邮箱",password_required:"请输入密码",verification_code_required:"请输入验证码",invitation_code_required:"请输入邀请码",invalid_email_format:"邮箱格式错误",send_code_failed:"发送验证码失败，请稍后重试",send_code_failed_log:"发送验证码失败:",registration_failed:"注册失败，请检查输入信息",registration_failed_log:"注册失败:",try_reload_or_later:"请尝试重新加载或稍后再试",password_change_failed:"修改密码失败，请稍后重试",password_change_failed_log:"修改密码失败:",password_too_short:"密码长度至少为6位",password_must_contain_letters_numbers:"密码必须包含字母和数字",server_timeout:"服务器超时，请稍后重试"},nav:{market:"行情",points:"积分",settings:"设置"},points:{my_points:"我的积分",total_points:"总积分",ranking:"排名",earn_points:"获取积分",invite_friends:"邀请好友",invite:"邀请",daily_trade:"每日交易",points:"积分",go_trade:"去交易",history:"积分记录",history_all:"全部",history_earned:"获得",history_used:"使用",no_history:"暂无积分记录",invitation_code:"邀请码",invitation_records:"邀请记录",no_invitation_records:"暂无邀请记录",total_invited:"已邀请 {count} 人",invitation_reward:"每邀请一位好友注册成功，可获得 {points} 积分奖励",copy_success:"复制成功",share_invitation_title:"邀请好友加入Cooltrade",share_invitation_text:"我正在使用Cooltrade进行加密货币分析，邀请你一起加入！使用我的邀请码 {code} 注册，我们都可以获得 {points} 积分奖励。",user:"用户",your_invitation_code:"你的邀请码",share:"分享",invitation_reward_desc:"邀请好友奖励",daily_trade_desc:"每日交易奖励",used_for_discount:"使用积分抵扣",registered_at:"注册时间"},tokenNotFound:{title:"{symbol} 数据未找到",description:"该代币尚未在我们的数据库中，点击下方按钮获取最新数据",loading:"正在获取最新市场数据...",refreshButton:"获取最新市场数据",not_supported:"该代币暂不支持，请尝试其他代币"},indicatorExplanations:{RSI:"相对强弱指数（RSI），用于衡量价格动量和超买超卖状态。",BIAS:"乖离率，衡量价格偏离均线的程度。",PSY:"心理线指标，反映市场参与者的心理变化。",VWAP:"成交量加权平均价，反映市场真实交易价值。",FundingRate:"资金费率，反映合约市场多空力量对比。",ExchangeNetflow:"交易所净流入，反映资金流向。",NUPL:"未实现净盈亏比率，反映市场整体盈亏状况。",MayerMultiple:"梅耶倍数，当前价格与200日均线的比值。",MACD:"移动平均线收敛散度，用于判断趋势强弱和转折点。",BollingerBands:"布林带，用于衡量价格波动性和支撑阻力位。",DMI:"动向指标，用于判断趋势方向和强度。"},search:{title:"搜索资产",placeholder:"搜索代码或名称...",searching:"搜索中...",no_results:"未找到结果",popular:"热门资产"},favorites:{add:"添加到收藏",remove:"取消收藏",title:"我的收藏",empty:"暂无收藏",empty_hint:"点击星标按钮添加收藏",added:"已添加到收藏",removed:"已取消收藏"}},Zt={common:{app_name:"Cooltrade",loading:"Loading...",refresh:"Refresh",refreshing:"Refreshing...",save:"Save",cancel:"Cancel",confirm:"Confirm",back:"Back",success:"Success",error:"Error",warning:"Warning",info:"Info",yes:"Yes",no:"No",ok:"OK",language:"Language",sending:"Sending...",registering:"Registering...",submitting:"Submitting...",retry:"Retry",privacy_policy:"Privacy Policy",about_us:"About Us",no_data:"No data available",load_data:"Load Data",popular_tokens:"Popular Tokens",popular_stocks:"Popular Stocks",quick_switch:"Quick Switch",search:"Search",coming_soon:"Soon",my_favorites:"My Favorites",items:"items",search_crypto_placeholder:"Search crypto symbols or names...",search_stock_placeholder:"Search stock symbols or names..."},market:{crypto:"Crypto",stock:"US Stock",china:"A-Share"},auth:{login:"Login",register:"Register",logout:"Logout",email:"Email",password:"Password",confirm_password:"Confirm Password",verification_code:"Verification Code",send_code:"Send Code",forgot_password:"Forgot Password",reset_password:"Reset Password",change_password:"Change Password",current_password:"Current Password",new_password:"New Password",confirm_new_password:"Confirm Password",invitation_code:"Invitation Code",login_success:"Login Successful",register_success:"Registration Successful",logout_success:"Logout Successful",password_changed:"Password Changed",password_reset:"Password Reset",code_sent:"Verification Code Sent",no_account:"No account?",register_now:"Register Now",have_account:"Have an account?",login_now:"Login Now",email_placeholder:"Enter your email",password_placeholder:"Enter your password",verification_code_placeholder:"Enter verification code",invitation_code_placeholder:"Enter invitation code",retry_in_seconds:"Retry in {seconds}s",password_requirements:"Password must be at least 6 characters and contain both letters and numbers",password_changed_success:"Password changed successfully",login_required_for_password_change:"Please login to change password",go_to_login:"Go to Login",enter_current_and_new_password:"Please enter your current and new password",new_password_placeholder:"Enter new password",confirm_new_password_placeholder:"Confirm new password",resetting:"Resetting...",reset_success:"Password Reset Successful",reset_success_message:"Your password has been reset successfully. Please login with your new password.",back_to_login:"Back to Login"},profile:{profile:"Profile",username:"Username",email:"Email",created_at:"Created At",updated_at:"Updated At",registration_time:"Registration Time",language_preference:"Language Preference",generate_invitation:"Generate Invitation Code",invitation_codes:"Invitation Codes",language_settings:"Language Settings",select_language:"Select Language",chinese:"Chinese",english:"English",japanese:"Japanese",korean:"Korean"},analysis:{technical_analysis:"Technical Analysis",market_data:"Market Data",trend_analysis:"Trend Analysis",indicators:"Indicators",trading_advice:"Trading Advice",risk_assessment:"Risk Assessment",current_price:"Current Price",last_update:"Last Update",refresh_data:"Refresh Data",refreshing:"Refreshing...",up_probability:"Up Probability",sideways_probability:"Sideways Probability",down_probability:"Down Probability",trend_summary:"Trend Summary",action:"Action",reason:"Reason",entry_price:"Entry Price",stop_loss:"Stop Loss",take_profit:"Take Profit",risk_level:"Risk Level",risk_score:"Risk Score",risk_details:"Risk Details",high_risk:"High Risk",medium_risk:"Medium Risk",low_risk:"Low Risk",market_report:"{symbol} Market Analysis",snapshot_price:"Snapshot Price",share_to_twitter:"Share to Twitter",save_image:"Save Image",uptrend:"Uptrend",sideways:"Sideways",downtrend:"Downtrend",market_trend_analysis:"Market Trend Analysis",technical_indicators:"Technical Indicators",recommended_action:"Recommended Action",risk_factors:"Risk Factors",refreshing_data:"Refreshing Data",refreshing_data_ellipsis:"Refreshing data...",calculating_indicators:"Fetching market data and calculating technical indicators...",analyzing_trends:"Analyzing trends and probabilities...",generating_advice:"Generating trading advice and risk assessment...",finalizing_data:"Finalizing data integration...",force_refresh:"Force Refresh",minute_ago:"{n} min ago",hour_ago:"{n} hours ago",day_ago:"{n} days ago",preparing_analysis_report:"Preparing analysis report...",generating_new_report:"Generating new analysis report, please wait...",please_wait:"Please wait, this may take some time",timeout_error:"Request timeout. Server is processing, please try again later.",refresh_report:"Refresh Report",refresh_report_too_soon:"The report cannot be refreshed until 12 hours have passed"},tokenNotFound:{title:"{symbol} Data Not Found",description:"This token is not yet in our database. Click the button below to get the latest data.",refreshButton:"Get Latest Market Data",not_supported:"This token is not supported yet, please try other tokens"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"Bollinger Bands",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"Funding Rate",exchange_netflow:"Exchange Netflow",nupl:"NUPL",mayer_multiple:"Mayer Multiple"},errors:{network_error:"Network Error",server_error:"Server Error",not_found:"Not Found",unauthorized:"Unauthorized",forbidden:"Forbidden",validation_error:"Validation Error",unknown_error:"Unknown Error",token_expired:"Login expired, please login again",invalid_credentials:"Invalid email or password",invalid_code:"Invalid or expired verification code",passwords_not_match:"Passwords do not match",email_already_registered:"Email already registered",email_not_registered:"Email not registered",invalid_invitation_code:"Invalid or used invitation code",weak_password:"Password is too weak",fill_all_fields:"Please fill in all required fields",login_failed_no_token:"Login failed: No token received",login_failed_server_error:"Login failed: Server response error",login_failed_check_input:"Login failed, please check your input",too_many_attempts:"Too many login attempts, please try again later",network_timeout:"Network connection timeout, please check your network",login_failed_try_later:"Login failed, please try again later",email_required:"Please enter your email",password_required:"Please enter your password",verification_code_required:"Please enter verification code",invitation_code_required:"Please enter invitation code",invalid_email_format:"Invalid email format",send_code_failed:"Failed to send verification code, please try again later",send_code_failed_log:"Failed to send verification code:",registration_failed:"Registration failed, please check your input",registration_failed_log:"Registration failed:",try_reload_or_later:"Please try reloading or try again later",password_change_failed:"Failed to change password, please try again later",password_change_failed_log:"Failed to change password:",password_too_short:"Password must be at least 6 characters",password_must_contain_letters_numbers:"Password must contain both letters and numbers",server_timeout:"Server timeout, please try again later",not_logged_in:"Not logged in",no_response_from_server:"No response from server",refresh_failed:"Refresh failed, please try again"},nav:{market:"Market",points:"Points",settings:"Settings"},points:{my_points:"My Points",total_points:"Total Points",ranking:"Ranking",earn_points:"Earn Points",invite_friends:"Invite Friends",invite:"Invite",daily_trade:"Daily Trade",points:"Points",go_trade:"Go Trade",history:"Points History",history_all:"All",history_earned:"Earned",history_used:"Used",no_history:"No points history",invitation_code:"Invitation Code",invitation_records:"Invitation Records",no_invitation_records:"No invitation records yet",total_invited:"{count} people invited",invitation_reward:"Earn {points} points for each friend who registers with your code",copy_success:"Copied successfully",share_invitation_title:"Join Cooltrade",share_invitation_text:"I'm using Cooltrade for crypto analysis and I invite you to join! Use my invitation code {code} to register and we'll both get {points} points.",user:"User",your_invitation_code:"Your Invitation Code",share:"Share",invitation_reward_desc:"Invitation Reward",daily_trade_desc:"Daily Trade Reward",used_for_discount:"Used for Discount",registered_at:"Registered at"},indicatorExplanations:{RSI:"Relative Strength Index (RSI), measures price momentum and overbought/oversold conditions.",BIAS:"Bias, measures the deviation of price from the moving average.",PSY:"Psychological Line, reflects market participants' psychological changes.",VWAP:"Volume Weighted Average Price, reflects the true trading value of the market.",FundingRate:"Funding Rate, reflects the balance of long and short forces in the contract market.",ExchangeNetflow:"Exchange Netflow, reflects the direction of capital flow.",NUPL:"Net Unrealized Profit/Loss, reflects the overall profit and loss status of the market.",MayerMultiple:"Mayer Multiple, the ratio of the current price to the 200-day moving average.",MACD:"Moving Average Convergence Divergence, used to judge trend strength and turning points.",BollingerBands:"Bollinger Bands, measures price volatility and support/resistance levels.",DMI:"Directional Movement Index, used to judge trend direction and strength."},search:{title:"Search Assets",placeholder:"Search symbols or names...",searching:"Searching...",no_results:"No results found",popular:"Popular Assets"},favorites:{add:"Add to Favorites",remove:"Remove from Favorites",title:"My Favorites",empty:"No favorites yet",empty_hint:"Click the star button to add favorites",added:"Added to favorites",removed:"Removed from favorites"}},er={points:{my_points:"マイポイント",total_points:"合計ポイント",ranking:"ランキング",earn_points:"ポイント獲得",invite_friends:"友達招待",invite:"招待",points:"ポイント",history:"ポイント履歴",history_all:"全て",history_earned:"獲得",history_used:"使用",no_history:"ポイント履歴がありません",invitation_reward_desc:"招待報酬",daily_trade_desc:"日次取引報酬",used_for_discount:"ポイント割引に使用",your_invitation_code:"あなたの招待コード",share:"シェア",copy_success:"コピー完了",share_invitation_title:"友達を招待",share_invitation_text:"私の招待コード {code} を使って登録すると、{points} ポイントがもらえます！",daily_trade:"デイリートレード",go_trade:"トレードへ",invitation_code:"招待コード",invitation_records:"招待履歴",no_invitation_records:"招待履歴がありません",total_invited:"{count}人を招待済み",invitation_reward:"友達が招待コードで登録すると{points}ポイントを獲得できます",user:"ユーザー",registered_at:"登録日時"},common:{app_name:"Cooltrade",loading:"読み込み中...",refresh:"更新",refreshing:"更新中...",save:"保存",cancel:"キャンセル",confirm:"確認",back:"戻る",success:"成功",error:"エラー",warning:"警告",info:"情報",yes:"はい",no:"いいえ",ok:"OK",language:"言語",sending:"送信中...",registering:"登録中...",submitting:"送信中...",retry:"再試行",privacy_policy:"プライバシーポリシー",about_us:"会社概要",no_data:"データがありません",load_data:"データを読み込む",popular_tokens:"人気トークン",popular_stocks:"人気株式",quick_switch:"クイック切替",search:"検索",coming_soon:"開発中",my_favorites:"お気に入り"},auth:{login:"ログイン",register:"登録",logout:"ログアウト",email:"メールアドレス",password:"パスワード",confirm_password:"パスワード確認",verification_code:"認証コード",send_code:"コードを送信",forgot_password:"パスワードを忘れた",reset_password:"パスワードをリセット",change_password:"パスワードを変更",current_password:"現在のパスワード",new_password:"新しいパスワード",confirm_new_password:"パスワード確認",invitation_code:"招待コード",login_success:"ログイン成功",register_success:"登録成功",logout_success:"ログアウト成功",password_changed:"パスワード変更完了",password_reset:"パスワードリセット完了",code_sent:"認証コードを送信しました",no_account:"アカウントをお持ちでない方",register_now:"今すぐ登録",have_account:"アカウントをお持ちの方",login_now:"ログイン",email_placeholder:"メールアドレスを入力",password_placeholder:"パスワードを入力",verification_code_placeholder:"認証コードを入力",invitation_code_placeholder:"招待コードを入力",retry_in_seconds:"{seconds}秒後に再試行",password_requirements:"パスワードは6文字以上で、文字と数字を含める必要があります",password_changed_success:"パスワードの変更が完了しました",login_required_for_password_change:"パスワードを変更するにはログインが必要です",go_to_login:"ログインへ",enter_current_and_new_password:"現在のパスワードと新しいパスワードを入力してください",new_password_placeholder:"新しいパスワードを入力",confirm_new_password_placeholder:"新しいパスワードを確認",resetting:"リセット中...",reset_success:"パスワードのリセットが完了しました",reset_success_message:"パスワードのリセットが完了しました。新しいパスワードでログインしてください。",back_to_login:"ログインに戻る"},profile:{profile:"プロフィール",username:"ユーザー名",email:"メールアドレス",created_at:"作成日時",updated_at:"更新日時",registration_time:"登録日時",language_preference:"言語設定",generate_invitation:"招待コード生成",invitation_codes:"招待コード一覧",language_settings:"言語設定",select_language:"言語を選択",chinese:"中国語",english:"英語",japanese:"日本語",korean:"韓国語"},analysis:{technical_analysis:"テクニカル分析",market_data:"市場データ",trend_analysis:"トレンド分析",indicators:"指標",trading_advice:"取引アドバイス",risk_assessment:"リスク評価",current_price:"現在価格",last_update:"最終更新",refresh_data:"データ更新",refreshing:"更新中...",up_probability:"上昇確率",sideways_probability:"横ばい確率",down_probability:"下落確率",trend_summary:"トレンド概要",action:"アクション",reason:"理由",entry_price:"エントリー価格",stop_loss:"ストップロス",take_profit:"利益確定",risk_level:"リスクレベル",risk_score:"リスクスコア",risk_details:"リスク詳細",high_risk:"高リスク",medium_risk:"中リスク",low_risk:"低リスク",market_report:"{symbol}市場分析",snapshot_price:"スナップショット価格",share_to_twitter:"Twitterでシェア",save_image:"画像を保存",uptrend:"上昇",sideways:"横ばい",downtrend:"下落",market_trend_analysis:"市場トレンド分析",technical_indicators:"テクニカル指標",recommended_action:"推奨アクション",risk_factors:"リスク要因",refreshing_data:"データ更新中",refreshing_data_ellipsis:"データ更新中...",calculating_indicators:"市場データ取得とテクニカル指標計算中...",analyzing_trends:"トレンドと確率を分析中...",generating_advice:"取引アドバイスとリスク評価を生成中...",finalizing_data:"データ統合を完了中...",force_refresh:"強制更新",minute_ago:"{n}分前",hour_ago:"{n}時間前",day_ago:"{n}日前",preparing_analysis_report:"分析レポートを準備中...",generating_new_report:"新しい分析レポートを生成中、お待ちください...",please_wait:"お待ちください、時間がかかる場合があります",timeout_error:"リクエストタイムアウト。サーバーが処理中です。後でもう一度お試しください",refresh_report:"レポート更新",refresh_report_too_soon:"レポートは12時間経過するまで更新できません"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"ボリンジャーバンド",bias:"バイアス",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"資金調達率",exchange_netflow:"取引所ネットフロー",nupl:"NUPL",mayer_multiple:"メイヤー倍数"},errors:{network_error:"ネットワークエラー",server_error:"サーバーエラー",not_found:"見つかりません",unauthorized:"認証されていません",forbidden:"アクセス禁止",validation_error:"検証エラー",unknown_error:"不明なエラー",token_expired:"ログインの有効期限が切れました。再度ログインしてください",invalid_credentials:"メールアドレスまたはパスワードが無効です",invalid_code:"無効または期限切れの認証コード",passwords_not_match:"パスワードが一致しません",email_already_registered:"このメールアドレスは既に登録されています",email_not_registered:"このメールアドレスは登録されていません",invalid_invitation_code:"無効または使用済みの招待コード",weak_password:"パスワードが弱すぎます",fill_all_fields:"必須項目をすべて入力してください",login_failed_no_token:"ログイン失敗：トークンが受信されませんでした",login_failed_server_error:"ログイン失敗：サーバー応答エラー",login_failed_check_input:"ログイン失敗、入力を確認してください",too_many_attempts:"ログイン試行回数が多すぎます。後でもう一度お試しください",network_timeout:"ネットワーク接続タイムアウト、ネットワークを確認してください",login_failed_try_later:"ログイン失敗、後でもう一度お試しください",email_required:"メールアドレスを入力してください",password_required:"パスワードを入力してください",verification_code_required:"認証コードを入力してください",invitation_code_required:"招待コードを入力してください",invalid_email_format:"無効なメールアドレス形式",send_code_failed:"認証コードの送信に失敗しました。後でもう一度お試しください",send_code_failed_log:"認証コードの送信に失敗しました:",registration_failed:"登録に失敗しました。入力情報を確認してください",registration_failed_log:"登録に失敗しました:",try_reload_or_later:"再読み込みするか、後でもう一度お試しください",password_change_failed:"パスワードの変更に失敗しました。後でもう一度お試しください",password_change_failed_log:"パスワードの変更に失敗しました:",password_too_short:"パスワードは6文字以上である必要があります",password_must_contain_letters_numbers:"パスワードには文字と数字の両方を含める必要があります",server_timeout:"サーバータイムアウト、後でもう一度お試しください"},nav:{market:"マーケット",points:"ポイント",settings:"設定"},tokenNotFound:{title:"{symbol} データが見つかりません",description:"このトークンはまだデータベースにありません。下のボタンをクリックして最新データを取得してください",loading:"最新の市場データを取得中...",refreshButton:"最新の市場データを取得"},indicatorExplanations:{RSI:"相対力指数（RSI）、価格のモメンタムや買われ過ぎ・売られ過ぎ状態を測定します。",BIAS:"乖離率、価格が移動平均線からどれだけ乖離しているかを示します。",PSY:"サイコロジカルライン、投資家心理の変化を反映します。",VWAP:"出来高加重平均価格、市場の実際の取引価値を示します。",FundingRate:"資金調達率、先物市場のロング・ショートの力関係を反映します。",ExchangeNetflow:"取引所純流入、資金の流れを示します。",NUPL:"未実現損益比率、市場全体の損益状況を示します。",MayerMultiple:"メイヤー・マルチプル、現在価格と200日移動平均線の比率です。",MACD:"移動平均収束拡散法（MACD）、トレンドの強さや転換点を判断します。",BollingerBands:"ボリンジャーバンド、価格の変動性やサポート・レジスタンスを測定します。",DMI:"方向性指数（DMI）、トレンドの方向や強さを判断します。"},market:{crypto:"暗号通貨",stock:"米国株",china:"A株"},search:{title:"資産検索",placeholder:"シンボルまたは名前を検索...",searching:"検索中...",no_results:"結果が見つかりません",popular:"人気資産"},favorites:{add:"お気に入りに追加",remove:"お気に入りから削除",title:"マイお気に入り",empty:"お気に入りがありません",added:"お気に入りに追加しました",removed:"お気に入りから削除しました"}},tr={points:{my_points:"내 포인트",total_points:"총 포인트",ranking:"랭킹",earn_points:"포인트 획득",invite_friends:"친구 초대",invite:"초대",points:"포인트",history:"포인트 내역",history_all:"전체",history_earned:"획득",history_used:"사용",no_history:"포인트 내역이 없습니다",invitation_reward_desc:"초대 보상",daily_trade_desc:"일일 거래 보상",used_for_discount:"포인트 할인 사용",your_invitation_code:"내 초대 코드",share:"공유",copy_success:"복사 완료",share_invitation_title:"친구 초대하기",share_invitation_text:"내 초대 코드 {code}로 가입하고 {points} 포인트를 받으세요!",daily_trade:"일일 거래",go_trade:"거래하기",invitation_code:"초대 코드",invitation_records:"초대 내역",no_invitation_records:"초대 내역이 없습니다",total_invited:"{count}명 초대됨",invitation_reward:"친구가 초대 코드로 가입하면 {points}포인트를 획득할 수 있습니다",user:"사용자",registered_at:"가입일"},common:{app_name:"Cooltrade",loading:"로딩 중...",refresh:"새로고침",refreshing:"새로고침 중...",save:"저장",cancel:"취소",confirm:"확인",back:"뒤로",success:"성공",error:"오류",warning:"경고",info:"정보",yes:"예",no:"아니오",ok:"확인",language:"언어",sending:"전송 중...",registering:"등록 중...",submitting:"제출 중...",retry:"다시 시도",privacy_policy:"개인정보 처리방침",about_us:"회사 소개",no_data:"데이터가 없습니다",load_data:"데이터 로드",popular_tokens:"인기 토큰",popular_stocks:"인기 주식",quick_switch:"빠른 전환",search:"검색",coming_soon:"개발 중",my_favorites:"내 즐겨찾기"},auth:{login:"로그인",register:"회원가입",logout:"로그아웃",email:"이메일",password:"비밀번호",confirm_password:"비밀번호 확인",verification_code:"인증 코드",send_code:"코드 전송",forgot_password:"비밀번호 찾기",reset_password:"비밀번호 재설정",change_password:"비밀번호 변경",current_password:"현재 비밀번호",new_password:"새 비밀번호",confirm_new_password:"비밀번호 확인",invitation_code:"초대 코드",login_success:"로그인 성공",register_success:"회원가입 성공",logout_success:"로그아웃 성공",password_changed:"비밀번호가 변경되었습니다",password_reset:"비밀번호가 재설정되었습니다",code_sent:"인증 코드가 전송되었습니다",no_account:"계정이 없으신가요?",register_now:"지금 가입하기",have_account:"계정이 있으신가요?",login_now:"로그인하기",email_placeholder:"이메일을 입력하세요",password_placeholder:"비밀번호를 입력하세요",verification_code_placeholder:"인증 코드를 입력하세요",invitation_code_placeholder:"초대 코드를 입력하세요",retry_in_seconds:"{seconds}초 후 다시 시도",password_requirements:"비밀번호는 6자 이상이며 문자와 숫자를 포함해야 합니다",password_changed_success:"비밀번호가 성공적으로 변경되었습니다",login_required_for_password_change:"비밀번호를 변경하려면 로그인이 필요합니다",go_to_login:"로그인으로 이동",enter_current_and_new_password:"현재 비밀번호와 새 비밀번호를 입력하세요",new_password_placeholder:"새 비밀번호를 입력하세요",confirm_new_password_placeholder:"새 비밀번호를 확인하세요",resetting:"재설정 중...",reset_success:"비밀번호 재설정 성공",reset_success_message:"비밀번호가 성공적으로 재설정되었습니다. 새 비밀번호로 로그인하세요.",back_to_login:"로그인으로 돌아가기"},profile:{profile:"프로필",username:"사용자 이름",email:"이메일",created_at:"생성일",updated_at:"업데이트일",registration_time:"가입 시간",language_preference:"언어 설정",generate_invitation:"초대 코드 생성",invitation_codes:"초대 코드 목록",language_settings:"언어 설정",select_language:"언어 선택",chinese:"중국어",english:"영어",japanese:"일본어",korean:"한국어"},analysis:{technical_analysis:"기술적 분석",market_data:"시장 데이터",trend_analysis:"추세 분석",indicators:"지표",trading_advice:"거래 조언",risk_assessment:"위험 평가",current_price:"현재 가격",last_update:"마지막 업데이트",refresh_data:"데이터 새로고침",refreshing:"새로고침 중...",up_probability:"상승 확률",sideways_probability:"횡보 확률",down_probability:"하락 확률",trend_summary:"추세 요약",action:"행동",reason:"이유",entry_price:"진입 가격",stop_loss:"손절가",take_profit:"이익실현가",risk_level:"위험 수준",risk_score:"위험 점수",risk_details:"위험 세부 정보",high_risk:"높은 위험",medium_risk:"중간 위험",low_risk:"낮은 위험",market_report:"{symbol} 시장 분석",snapshot_price:"스냅샷 가격",share_to_twitter:"트위터에 공유",save_image:"이미지 저장",uptrend:"상승 추세",sideways:"횡보 추세",downtrend:"하락 추세",market_trend_analysis:"시장 추세 분석",technical_indicators:"기술적 지표",recommended_action:"추천 행동",risk_factors:"위험 요소",refreshing_data:"데이터 새로고침 중",refreshing_data_ellipsis:"데이터 새로고침 중...",calculating_indicators:"시장 데이터 및 기술적 지표 계산 중...",analyzing_trends:"추세 및 확률 분석 중...",generating_advice:"거래 조언 및 위험 평가 생성 중...",finalizing_data:"데이터 통합 완료 중...",force_refresh:"강제 새로고침",minute_ago:"{n}분 전",hour_ago:"{n}시간 전",day_ago:"{n}일 전",preparing_analysis_report:"분석 보고서 준비 중...",generating_new_report:"새로운 분석 보고서 생성 중, 잠시만 기다려주세요...",please_wait:"잠시 기다려 주세요, 시간이 걸릴 수 있습니다",timeout_error:"요청 시간 초과. 서버가 처리 중입니다. 나중에 다시 시도해주세요",refresh_report:"보고서 새로고침",refresh_report_too_soon:"보고서는 12시간이 지나야 새로 고침할 수 있습니다"},indicators:{rsi:"RSI",macd:"MACD",bollinger_bands:"볼린저 밴드",bias:"BIAS",psy:"PSY",dmi:"DMI",vwap:"VWAP",funding_rate:"자금 조달 비율",exchange_netflow:"거래소 순유입",nupl:"NUPL",mayer_multiple:"메이어 배수"},errors:{network_error:"네트워크 오류",server_error:"서버 오류",not_found:"찾을 수 없음",unauthorized:"인증되지 않음",forbidden:"접근 금지",validation_error:"유효성 검사 오류",unknown_error:"알 수 없는 오류",token_expired:"로그인이 만료되었습니다. 다시 로그인해주세요",invalid_credentials:"이메일 또는 비밀번호가 잘못되었습니다",invalid_code:"유효하지 않거나 만료된 인증 코드",passwords_not_match:"비밀번호가 일치하지 않습니다",email_already_registered:"이미 등록된 이메일입니다",email_not_registered:"등록되지 않은 이메일입니다",invalid_invitation_code:"유효하지 않거나 사용된 초대 코드",weak_password:"비밀번호가 너무 약합니다",fill_all_fields:"모든 필수 항목을 입력해주세요",login_failed_no_token:"로그인 실패: 토큰을 받지 못했습니다",login_failed_server_error:"로그인 실패: 서버 응답 오류",login_failed_check_input:"로그인 실패, 입력을 확인해주세요",too_many_attempts:"로그인 시도 횟수가 너무 많습니다. 나중에 다시 시도해주세요",network_timeout:"네트워크 연결 시간 초과, 네트워크를 확인해주세요",login_failed_try_later:"로그인 실패, 나중에 다시 시도해주세요",email_required:"이메일을 입력해주세요",password_required:"비밀번호를 입력해주세요",verification_code_required:"인증 코드를 입력해주세요",invitation_code_required:"초대 코드를 입력해주세요",invalid_email_format:"잘못된 이메일 형식",send_code_failed:"인증 코드 전송에 실패했습니다. 나중에 다시 시도해주세요",send_code_failed_log:"인증 코드 전송 실패:",registration_failed:"등록에 실패했습니다. 입력 정보를 확인해주세요",registration_failed_log:"등록 실패:",try_reload_or_later:"다시 로드하거나 나중에 다시 시도해주세요",password_change_failed:"비밀번호 변경에 실패했습니다. 나중에 다시 시도해주세요",password_change_failed_log:"비밀번호 변경 실패:",password_too_short:"비밀번호는 6자 이상이어야 합니다",password_must_contain_letters_numbers:"비밀번호는 문자와 숫자를 모두 포함해야 합니다",server_timeout:"서버 시간 초과, 나중에 다시 시도해주세요"},nav:{market:"마켓",points:"포인트",settings:"설정"},tokenNotFound:{title:"{symbol} 데이터를 찾을 수 없습니다",description:"이 토큰은 아직 데이터베이스에 없습니다. 아래 버튼을 클릭하여 최신 데이터를 가져오세요",loading:"최신 시장 데이터 가져오는 중...",refreshButton:"최신 시장 데이터 가져오기"},indicatorExplanations:{RSI:"상대강도지수(RSI), 가격 모멘텀과 과매수/과매도 상태를 측정합니다.",BIAS:"乖리율, 가격이 이동평균선에서 얼마나 벗어났는지 나타냅니다.",PSY:"심리선 지표, 시장 참여자의 심리 변화를 반영합니다.",VWAP:"거래량 가중 평균가, 시장의 실제 거래 가치를 반영합니다.",FundingRate:"펀딩비, 선물 시장의 롱/숏 세력 균형을 반영합니다.",ExchangeNetflow:"거래소 순유입, 자금 흐름 방향을 나타냅니다.",NUPL:"미실현 손익비율, 시장 전체의 손익 상태를 반영합니다.",MayerMultiple:"마이어 배수, 현재 가격과 200일 이동평균선의 비율입니다.",MACD:"이동평균 수렴확산(MACD), 추세의 강도와 전환점을 판단합니다.",BollingerBands:"볼린저 밴드, 가격 변동성과 지지/저항 구간을 측정합니다.",DMI:"방향성 지표(DMI), 추세 방향과 강도를 판단합니다."},market:{crypto:"암호화폐",stock:"미국 주식",china:"A주"},search:{title:"자산 검색",placeholder:"심볼 또는 이름 검색...",searching:"검색 중...",no_results:"결과를 찾을 수 없습니다",popular:"인기 자산"},favorites:{add:"즐겨찾기에 추가",remove:"즐겨찾기에서 제거",title:"내 즐겨찾기",empty:"즐겨찾기가 없습니다",added:"즐겨찾기에 추가되었습니다",removed:"즐겨찾기에서 제거되었습니다"}},rr={"zh-CN":Qt,"en-US":Zt,"ja-JP":er,"ko-KR":tr};let ar="en-US";function nr(e,t){const r=localStorage.getItem("language");r&&["zh-CN","en-US","ja-JP","ko-KR"].includes(r)&&r!==ar&&(ar=r);const a=e.split(".");let n=rr[ar];for(const o of a){if(null==n)return e;n=n[o]}return null==n?e:"string"==typeof n&&t?Object.entries(t).reduce(((e,[t,r])=>e.replace(new RegExp(`{${t}}`,"g"),String(r))),n):String(n)}function or(e){["zh-CN","en-US","ja-JP","ko-KR"].includes(e)&&(ar=e,localStorage.setItem("language",e),window.dispatchEvent(new CustomEvent("locale-changed",{detail:{locale:e}})))}function sr(){return ar}function ir(){return Object.keys(rr)}!function(){const e=localStorage.getItem("language");e&&["zh-CN","en-US","ja-JP","ko-KR"].includes(e)?ar=e:(ar="en-US",localStorage.setItem("language","en-US"))}();const lr={install(e){e.config.globalProperties.$td=nr,e.config.globalProperties.$locale={get:sr,set:or,available:ir()},e.directive("t",{mounted(e,t){e.textContent=nr(t.value)},updated(e,t){e.textContent=nr(t.value)}})}},cr={t:nr,setLocale:or,getLocale:sr,getAvailableLocales:ir,plugin:lr},dr=Object.freeze(Object.defineProperty({__proto__:null,default:cr,getAvailableLocales:ir,getLocale:sr,i18nDirectPlugin:lr,setLocale:or,t:nr},Symbol.toStringTag,{value:"Module"})),ur=h((()=>window.location.protocol.includes("extension")||window.location.protocol.includes("chrome")||window.location.protocol.includes("moz"))),pr=()=>{const{t:e,locale:t}=Ht(),r=()=>{const e=localStorage.getItem("language");e&&["zh-CN","en-US","ja-JP","ko-KR"].includes(e)&&e!==t.value&&(t.value=e)};return r(),window.addEventListener("language-changed",(e=>{var r;const a=(null==(r=e.detail)?void 0:r.language)||localStorage.getItem("language")||"en-US";t.value=a})),window.addEventListener("force-refresh-i18n",(()=>{r()})),{t:(t,a)=>{if(r(),ur.value){const r=nr(t,a);if(r===t){const n=e(t,a||{});return n===t?r:n}return r}return e(t,a||{})},locale:t}};function mr(e,t){return function(){return e.apply(t,arguments)}}const{toString:fr}=Object.prototype,{getPrototypeOf:gr}=Object,{iterator:_r,toStringTag:vr}=Symbol,hr=(e=>t=>{const r=fr.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),yr=e=>(e=e.toLowerCase(),t=>hr(t)===e),br=e=>t=>typeof t===e,{isArray:wr}=Array,xr=br("undefined"),kr=yr("ArrayBuffer"),Sr=br("string"),Er=br("function"),Rr=br("number"),Cr=e=>null!==e&&"object"==typeof e,Or=e=>{if("object"!==hr(e))return!1;const t=gr(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||vr in e||_r in e)},Tr=yr("Date"),Pr=yr("File"),Ar=yr("Blob"),Ir=yr("FileList"),jr=yr("URLSearchParams"),[Nr,Fr,Lr,Dr]=["ReadableStream","Request","Response","Headers"].map(yr);function Ur(e,t,{allOwnKeys:r=!1}={}){if(null==e)return;let a,n;if("object"!=typeof e&&(e=[e]),wr(e))for(a=0,n=e.length;a<n;a++)t.call(null,e[a],a,e);else{const n=r?Object.getOwnPropertyNames(e):Object.keys(e),o=n.length;let s;for(a=0;a<o;a++)s=n[a],t.call(null,e[s],s,e)}}function Mr(e,t){t=t.toLowerCase();const r=Object.keys(e);let a,n=r.length;for(;n-- >0;)if(a=r[n],t===a.toLowerCase())return a;return null}const Br="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,zr=e=>!xr(e)&&e!==Br,qr=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&gr(Uint8Array)),Wr=yr("HTMLFormElement"),Vr=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),$r=yr("RegExp"),Jr=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),a={};Ur(r,((r,n)=>{let o;!1!==(o=t(r,n,e))&&(a[n]=o||r)})),Object.defineProperties(e,a)},Kr=yr("AsyncFunction"),Hr=(Gr="function"==typeof setImmediate,Yr=Er(Br.postMessage),Gr?setImmediate:Yr?(Xr=`axios@${Math.random()}`,Qr=[],Br.addEventListener("message",(({source:e,data:t})=>{e===Br&&t===Xr&&Qr.length&&Qr.shift()()}),!1),e=>{Qr.push(e),Br.postMessage(Xr,"*")}):e=>setTimeout(e));var Gr,Yr,Xr,Qr;const Zr="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Br):"undefined"!=typeof process&&process.nextTick||Hr,ea={isArray:wr,isArrayBuffer:kr,isBuffer:function(e){return null!==e&&!xr(e)&&null!==e.constructor&&!xr(e.constructor)&&Er(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||Er(e.append)&&("formdata"===(t=hr(e))||"object"===t&&Er(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&kr(e.buffer),t},isString:Sr,isNumber:Rr,isBoolean:e=>!0===e||!1===e,isObject:Cr,isPlainObject:Or,isReadableStream:Nr,isRequest:Fr,isResponse:Lr,isHeaders:Dr,isUndefined:xr,isDate:Tr,isFile:Pr,isBlob:Ar,isRegExp:$r,isFunction:Er,isStream:e=>Cr(e)&&Er(e.pipe),isURLSearchParams:jr,isTypedArray:qr,isFileList:Ir,forEach:Ur,merge:function e(){const{caseless:t}=zr(this)&&this||{},r={},a=(a,n)=>{const o=t&&Mr(r,n)||n;Or(r[o])&&Or(a)?r[o]=e(r[o],a):Or(a)?r[o]=e({},a):wr(a)?r[o]=a.slice():r[o]=a};for(let n=0,o=arguments.length;n<o;n++)arguments[n]&&Ur(arguments[n],a);return r},extend:(e,t,r,{allOwnKeys:a}={})=>(Ur(t,((t,a)=>{r&&Er(t)?e[a]=mr(t,r):e[a]=t}),{allOwnKeys:a}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,a)=>{e.prototype=Object.create(t.prototype,a),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,a)=>{let n,o,s;const i={};if(t=t||{},null==e)return t;do{for(n=Object.getOwnPropertyNames(e),o=n.length;o-- >0;)s=n[o],a&&!a(s,e,t)||i[s]||(t[s]=e[s],i[s]=!0);e=!1!==r&&gr(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:hr,kindOfTest:yr,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;const a=e.indexOf(t,r);return-1!==a&&a===r},toArray:e=>{if(!e)return null;if(wr(e))return e;let t=e.length;if(!Rr(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{const r=(e&&e[_r]).call(e);let a;for(;(a=r.next())&&!a.done;){const r=a.value;t.call(e,r[0],r[1])}},matchAll:(e,t)=>{let r;const a=[];for(;null!==(r=e.exec(t));)a.push(r);return a},isHTMLForm:Wr,hasOwnProperty:Vr,hasOwnProp:Vr,reduceDescriptors:Jr,freezeMethods:e=>{Jr(e,((t,r)=>{if(Er(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;const a=e[r];Er(a)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")}))}))},toObjectSet:(e,t)=>{const r={},a=e=>{e.forEach((e=>{r[e]=!0}))};return wr(e)?a(e):a(String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,r){return t.toUpperCase()+r})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Mr,global:Br,isContextDefined:zr,isSpecCompliantForm:function(e){return!!(e&&Er(e.append)&&"FormData"===e[vr]&&e[_r])},toJSONObject:e=>{const t=new Array(10),r=(e,a)=>{if(Cr(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[a]=e;const n=wr(e)?[]:{};return Ur(e,((e,t)=>{const o=r(e,a+1);!xr(o)&&(n[t]=o)})),t[a]=void 0,n}}return e};return r(e,0)},isAsyncFn:Kr,isThenable:e=>e&&(Cr(e)||Er(e))&&Er(e.then)&&Er(e.catch),setImmediate:Hr,asap:Zr,isIterable:e=>null!=e&&Er(e[_r])};function ta(e,t,r,a,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),a&&(this.request=a),n&&(this.response=n,this.status=n.status?n.status:null)}ea.inherits(ta,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ea.toJSONObject(this.config),code:this.code,status:this.status}}});const ra=ta.prototype,aa={};function na(e){return ea.isPlainObject(e)||ea.isArray(e)}function oa(e){return ea.endsWith(e,"[]")?e.slice(0,-2):e}function sa(e,t,r){return e?e.concat(t).map((function(e,t){return e=oa(e),!r&&t?"["+e+"]":e})).join(r?".":""):t}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{aa[e]={value:e}})),Object.defineProperties(ta,aa),Object.defineProperty(ra,"isAxiosError",{value:!0}),ta.from=(e,t,r,a,n,o)=>{const s=Object.create(ra);return ea.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),ta.call(s,e.message,t,r,a,n),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const ia=ea.toFlatObject(ea,{},null,(function(e){return/^is[A-Z]/.test(e)}));function la(e,t,r){if(!ea.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const a=(r=ea.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!ea.isUndefined(t[e])}))).metaTokens,n=r.visitor||c,o=r.dots,s=r.indexes,i=(r.Blob||"undefined"!=typeof Blob&&Blob)&&ea.isSpecCompliantForm(t);if(!ea.isFunction(n))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(ea.isDate(e))return e.toISOString();if(!i&&ea.isBlob(e))throw new ta("Blob is not supported. Use a Buffer instead.");return ea.isArrayBuffer(e)||ea.isTypedArray(e)?i&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,r,n){let i=e;if(e&&!n&&"object"==typeof e)if(ea.endsWith(r,"{}"))r=a?r:r.slice(0,-2),e=JSON.stringify(e);else if(ea.isArray(e)&&function(e){return ea.isArray(e)&&!e.some(na)}(e)||(ea.isFileList(e)||ea.endsWith(r,"[]"))&&(i=ea.toArray(e)))return r=oa(r),i.forEach((function(e,a){!ea.isUndefined(e)&&null!==e&&t.append(!0===s?sa([r],a,o):null===s?r:r+"[]",l(e))})),!1;return!!na(e)||(t.append(sa(n,r,o),l(e)),!1)}const d=[],u=Object.assign(ia,{defaultVisitor:c,convertValue:l,isVisitable:na});if(!ea.isObject(e))throw new TypeError("data must be an object");return function e(r,a){if(!ea.isUndefined(r)){if(-1!==d.indexOf(r))throw Error("Circular reference detected in "+a.join("."));d.push(r),ea.forEach(r,(function(r,o){!0===(!(ea.isUndefined(r)||null===r)&&n.call(t,r,ea.isString(o)?o.trim():o,a,u))&&e(r,a?a.concat(o):[o])})),d.pop()}}(e),t}function ca(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function da(e,t){this._pairs=[],e&&la(e,this,t)}const ua=da.prototype;function pa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ma(e,t,r){if(!t)return e;const a=r&&r.encode||pa;ea.isFunction(r)&&(r={serialize:r});const n=r&&r.serialize;let o;if(o=n?n(t,r):ea.isURLSearchParams(t)?t.toString():new da(t,r).toString(a),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}ua.append=function(e,t){this._pairs.push([e,t])},ua.toString=function(e){const t=e?function(t){return e.call(this,t,ca)}:ca;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class fa{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ea.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const ga={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},_a={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:da,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},va="undefined"!=typeof window&&"undefined"!=typeof document,ha="object"==typeof navigator&&navigator||void 0,ya=va&&(!ha||["ReactNative","NativeScript","NS"].indexOf(ha.product)<0),ba="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,wa=va&&window.location.href||"http://localhost",xa=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:va,hasStandardBrowserEnv:ya,hasStandardBrowserWebWorkerEnv:ba,navigator:ha,origin:wa},Symbol.toStringTag,{value:"Module"})),ka=c(c({},xa),_a);function Sa(e){function t(e,r,a,n){let o=e[n++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),i=n>=e.length;return o=!o&&ea.isArray(a)?a.length:o,i?(ea.hasOwnProp(a,o)?a[o]=[a[o],r]:a[o]=r,!s):(a[o]&&ea.isObject(a[o])||(a[o]=[]),t(e,r,a[o],n)&&ea.isArray(a[o])&&(a[o]=function(e){const t={},r=Object.keys(e);let a;const n=r.length;let o;for(a=0;a<n;a++)o=r[a],t[o]=e[o];return t}(a[o])),!s)}if(ea.isFormData(e)&&ea.isFunction(e.entries)){const r={};return ea.forEachEntry(e,((e,a)=>{t(function(e){return ea.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),a,r,0)})),r}return null}const Ea={transitional:ga,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const r=t.getContentType()||"",a=r.indexOf("application/json")>-1,n=ea.isObject(e);if(n&&ea.isHTMLForm(e)&&(e=new FormData(e)),ea.isFormData(e))return a?JSON.stringify(Sa(e)):e;if(ea.isArrayBuffer(e)||ea.isBuffer(e)||ea.isStream(e)||ea.isFile(e)||ea.isBlob(e)||ea.isReadableStream(e))return e;if(ea.isArrayBufferView(e))return e.buffer;if(ea.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(n){if(r.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return la(e,new ka.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,a){return ka.isNode&&ea.isBuffer(e)?(this.append(t,e.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=ea.isFileList(e))||r.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return la(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return n||a?(t.setContentType("application/json",!1),function(e,t,r){if(ea.isString(e))try{return(t||JSON.parse)(e),ea.trim(e)}catch(a){if("SyntaxError"!==a.name)throw a}return(r||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Ea.transitional,r=t&&t.forcedJSONParsing,a="json"===this.responseType;if(ea.isResponse(e)||ea.isReadableStream(e))return e;if(e&&ea.isString(e)&&(r&&!this.responseType||a)){const r=!(t&&t.silentJSONParsing)&&a;try{return JSON.parse(e)}catch(n){if(r){if("SyntaxError"===n.name)throw ta.from(n,ta.ERR_BAD_RESPONSE,this,null,this.response);throw n}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ka.classes.FormData,Blob:ka.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ea.forEach(["delete","get","head","post","put","patch"],(e=>{Ea.headers[e]={}}));const Ra=ea.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ca=Symbol("internals");function Oa(e){return e&&String(e).trim().toLowerCase()}function Ta(e){return!1===e||null==e?e:ea.isArray(e)?e.map(Ta):String(e)}function Pa(e,t,r,a,n){return ea.isFunction(a)?a.call(this,t,r):(n&&(t=r),ea.isString(t)?ea.isString(a)?-1!==t.indexOf(a):ea.isRegExp(a)?a.test(t):void 0:void 0)}let Aa=class{constructor(e){e&&this.set(e)}set(e,t,r){const a=this;function n(e,t,r){const n=Oa(t);if(!n)throw new Error("header name must be a non-empty string");const o=ea.findKey(a,n);(!o||void 0===a[o]||!0===r||void 0===r&&!1!==a[o])&&(a[o||t]=Ta(e))}const o=(e,t)=>ea.forEach(e,((e,r)=>n(e,r,t)));if(ea.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(ea.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let r,a,n;return e&&e.split("\n").forEach((function(e){n=e.indexOf(":"),r=e.substring(0,n).trim().toLowerCase(),a=e.substring(n+1).trim(),!r||t[r]&&Ra[r]||("set-cookie"===r?t[r]?t[r].push(a):t[r]=[a]:t[r]=t[r]?t[r]+", "+a:a)})),t})(e),t);else if(ea.isObject(e)&&ea.isIterable(e)){let r,a,n={};for(const t of e){if(!ea.isArray(t))throw TypeError("Object iterator must return a key-value pair");n[a=t[0]]=(r=n[a])?ea.isArray(r)?[...r,t[1]]:[r,t[1]]:t[1]}o(n,t)}else null!=e&&n(t,e,r);return this}get(e,t){if(e=Oa(e)){const r=ea.findKey(this,e);if(r){const e=this[r];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let a;for(;a=r.exec(e);)t[a[1]]=a[2];return t}(e);if(ea.isFunction(t))return t.call(this,e,r);if(ea.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Oa(e)){const r=ea.findKey(this,e);return!(!r||void 0===this[r]||t&&!Pa(0,this[r],r,t))}return!1}delete(e,t){const r=this;let a=!1;function n(e){if(e=Oa(e)){const n=ea.findKey(r,e);!n||t&&!Pa(0,r[n],n,t)||(delete r[n],a=!0)}}return ea.isArray(e)?e.forEach(n):n(e),a}clear(e){const t=Object.keys(this);let r=t.length,a=!1;for(;r--;){const n=t[r];e&&!Pa(0,this[n],n,e,!0)||(delete this[n],a=!0)}return a}normalize(e){const t=this,r={};return ea.forEach(this,((a,n)=>{const o=ea.findKey(r,n);if(o)return t[o]=Ta(a),void delete t[n];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,r)=>t.toUpperCase()+r))}(n):String(n).trim();s!==n&&delete t[n],t[s]=Ta(a),r[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return ea.forEach(this,((r,a)=>{null!=r&&!1!==r&&(t[a]=e&&ea.isArray(r)?r.join(", "):r)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const r=new this(e);return t.forEach((e=>r.set(e))),r}static accessor(e){const t=(this[Ca]=this[Ca]={accessors:{}}).accessors,r=this.prototype;function a(e){const a=Oa(e);t[a]||(function(e,t){const r=ea.toCamelCase(" "+t);["get","set","has"].forEach((a=>{Object.defineProperty(e,a+r,{value:function(e,r,n){return this[a].call(this,t,e,r,n)},configurable:!0})}))}(r,e),t[a]=!0)}return ea.isArray(e)?e.forEach(a):a(e),this}};function Ia(e,t){const r=this||Ea,a=t||r,n=Aa.from(a.headers);let o=a.data;return ea.forEach(e,(function(e){o=e.call(r,o,n.normalize(),t?t.status:void 0)})),n.normalize(),o}function ja(e){return!(!e||!e.__CANCEL__)}function Na(e,t,r){ta.call(this,null==e?"canceled":e,ta.ERR_CANCELED,t,r),this.name="CanceledError"}function Fa(e,t,r){const a=r.config.validateStatus;r.status&&a&&!a(r.status)?t(new ta("Request failed with status code "+r.status,[ta.ERR_BAD_REQUEST,ta.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r)):e(r)}Aa.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ea.reduceDescriptors(Aa.prototype,(({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}})),ea.freezeMethods(Aa),ea.inherits(Na,ta,{__CANCEL__:!0});const La=(e,t,r=3)=>{let a=0;const n=function(e,t){e=e||10;const r=new Array(e),a=new Array(e);let n,o=0,s=0;return t=void 0!==t?t:1e3,function(i){const l=Date.now(),c=a[s];n||(n=l),r[o]=i,a[o]=l;let d=s,u=0;for(;d!==o;)u+=r[d++],d%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),l-n<t)return;const p=c&&l-c;return p?Math.round(1e3*u/p):void 0}}(50,250);return function(e,t){let r,a,n=0,o=1e3/t;const s=(t,o=Date.now())=>{n=o,r=null,a&&(clearTimeout(a),a=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),i=t-n;i>=o?s(e,t):(r=e,a||(a=setTimeout((()=>{a=null,s(r)}),o-i)))},()=>r&&s(r)]}((r=>{const o=r.loaded,s=r.lengthComputable?r.total:void 0,i=o-a,l=n(i);a=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:i,rate:l||void 0,estimated:l&&s&&o<=s?(s-o)/l:void 0,event:r,lengthComputable:null!=s,[t?"download":"upload"]:!0})}),r)},Da=(e,t)=>{const r=null!=e;return[a=>t[0]({lengthComputable:r,total:e,loaded:a}),t[1]]},Ua=e=>(...t)=>ea.asap((()=>e(...t))),Ma=ka.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,ka.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(ka.origin),ka.navigator&&/(msie|trident)/i.test(ka.navigator.userAgent)):()=>!0,Ba=ka.hasStandardBrowserEnv?{write(e,t,r,a,n,o){const s=[e+"="+encodeURIComponent(t)];ea.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),ea.isString(a)&&s.push("path="+a),ea.isString(n)&&s.push("domain="+n),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function za(e,t,r){let a=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(a||0==r)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const qa=e=>e instanceof Aa?c({},e):e;function Wa(e,t){t=t||{};const r={};function a(e,t,r,a){return ea.isPlainObject(e)&&ea.isPlainObject(t)?ea.merge.call({caseless:a},e,t):ea.isPlainObject(t)?ea.merge({},t):ea.isArray(t)?t.slice():t}function n(e,t,r,n){return ea.isUndefined(t)?ea.isUndefined(e)?void 0:a(void 0,e,0,n):a(e,t,0,n)}function o(e,t){if(!ea.isUndefined(t))return a(void 0,t)}function s(e,t){return ea.isUndefined(t)?ea.isUndefined(e)?void 0:a(void 0,e):a(void 0,t)}function i(r,n,o){return o in t?a(r,n):o in e?a(void 0,r):void 0}const l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:i,headers:(e,t,r)=>n(qa(e),qa(t),0,!0)};return ea.forEach(Object.keys(Object.assign({},e,t)),(function(a){const o=l[a]||n,s=o(e[a],t[a],a);ea.isUndefined(s)&&o!==i||(r[a]=s)})),r}const Va=e=>{const t=Wa({},e);let r,{data:a,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:l}=t;if(t.headers=i=Aa.from(i),t.url=ma(za(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),ea.isFormData(a))if(ka.hasStandardBrowserEnv||ka.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(r=i.getContentType())){const[e,...t]=r?r.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(ka.hasStandardBrowserEnv&&(n&&ea.isFunction(n)&&(n=n(t)),n||!1!==n&&Ma(t.url))){const e=o&&s&&Ba.read(s);e&&i.set(o,e)}return t},$a="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,r){const a=Va(e);let n=a.data;const o=Aa.from(a.headers).normalize();let s,i,l,c,d,{responseType:u,onUploadProgress:p,onDownloadProgress:m}=a;function f(){c&&c(),d&&d(),a.cancelToken&&a.cancelToken.unsubscribe(s),a.signal&&a.signal.removeEventListener("abort",s)}let g=new XMLHttpRequest;function _(){if(!g)return;const a=Aa.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());Fa((function(e){t(e),f()}),(function(e){r(e),f()}),{data:u&&"text"!==u&&"json"!==u?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:a,config:e,request:g}),g=null}g.open(a.method.toUpperCase(),a.url,!0),g.timeout=a.timeout,"onloadend"in g?g.onloadend=_:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(_)},g.onabort=function(){g&&(r(new ta("Request aborted",ta.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new ta("Network Error",ta.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const n=a.transitional||ga;a.timeoutErrorMessage&&(t=a.timeoutErrorMessage),r(new ta(t,n.clarifyTimeoutError?ta.ETIMEDOUT:ta.ECONNABORTED,e,g)),g=null},void 0===n&&o.setContentType(null),"setRequestHeader"in g&&ea.forEach(o.toJSON(),(function(e,t){g.setRequestHeader(t,e)})),ea.isUndefined(a.withCredentials)||(g.withCredentials=!!a.withCredentials),u&&"json"!==u&&(g.responseType=a.responseType),m&&([l,d]=La(m,!0),g.addEventListener("progress",l)),p&&g.upload&&([i,c]=La(p),g.upload.addEventListener("progress",i),g.upload.addEventListener("loadend",c)),(a.cancelToken||a.signal)&&(s=t=>{g&&(r(!t||t.type?new Na(null,e,g):t),g.abort(),g=null)},a.cancelToken&&a.cancelToken.subscribe(s),a.signal&&(a.signal.aborted?s():a.signal.addEventListener("abort",s)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(a.url);v&&-1===ka.protocols.indexOf(v)?r(new ta("Unsupported protocol "+v+":",ta.ERR_BAD_REQUEST,e)):g.send(n||null)}))},Ja=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,a=new AbortController;const n=function(e){if(!r){r=!0,s();const t=e instanceof Error?e:this.reason;a.abort(t instanceof ta?t:new Na(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,n(new ta(`timeout ${t} of ms exceeded`,ta.ETIMEDOUT))}),t);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)})),e=null)};e.forEach((e=>e.addEventListener("abort",n)));const{signal:i}=a;return i.unsubscribe=()=>ea.asap(s),i}},Ka=function*(e,t){let r=e.byteLength;if(r<t)return void(yield e);let a,n=0;for(;n<r;)a=n+t,yield e.slice(n,a),n=a},Ha=function(e,t){return m(this,null,(function*(){try{for(var r,a,n,o=((e,t,r)=>(t=e[i("asyncIterator")])?t.call(e):(e=e[i("iterator")](),t={},(r=(r,a)=>(a=e[r])&&(t[r]=t=>new Promise(((r,n,o)=>(t=a.call(e,t),o=t.done,Promise.resolve(t.value).then((e=>r({value:e,done:o})),n))))))("next"),r("return"),t))(Ga(e));r=!(a=yield new p(o.next())).done;r=!1){const e=a.value;yield*f(Ka(e,t))}}catch(a){n=[a]}finally{try{r&&(a=o.return)&&(yield new p(a.call(o)))}finally{if(n)throw n[0]}}}))},Ga=function(e){return m(this,null,(function*(){if(e[Symbol.asyncIterator])return void(yield*f(e));const t=e.getReader();try{for(;;){const{done:e,value:r}=yield new p(t.read());if(e)break;yield r}}finally{yield new p(t.cancel())}}))},Ya=(e,t,r,a)=>{const n=Ha(e,t);let o,s=0,i=e=>{o||(o=!0,a&&a(e))};return new ReadableStream({pull(e){return u(this,null,(function*(){try{const{done:t,value:a}=yield n.next();if(t)return i(),void e.close();let o=a.byteLength;if(r){let e=s+=o;r(e)}e.enqueue(new Uint8Array(a))}catch(t){throw i(t),t}}))},cancel:e=>(i(e),n.return())},{highWaterMark:2})},Xa="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Qa=Xa&&"function"==typeof ReadableStream,Za=Xa&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):t=>u(e,null,(function*(){return new Uint8Array(yield new Response(t).arrayBuffer())}))),en=(e,...t)=>{try{return!!e(...t)}catch(r){return!1}},tn=Qa&&en((()=>{let e=!1;const t=new Request(ka.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),rn=Qa&&en((()=>ea.isReadableStream(new Response("").body))),an={stream:rn&&(e=>e.body)};var nn;Xa&&(nn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!an[e]&&(an[e]=ea.isFunction(nn[e])?t=>t[e]():(t,r)=>{throw new ta(`Response type '${e}' is not supported`,ta.ERR_NOT_SUPPORT,r)})})));const on=(t,r)=>u(e,null,(function*(){const a=ea.toFiniteNumber(t.getContentLength());return null==a?(t=>u(e,null,(function*(){if(null==t)return 0;if(ea.isBlob(t))return t.size;if(ea.isSpecCompliantForm(t)){const e=new Request(ka.origin,{method:"POST",body:t});return(yield e.arrayBuffer()).byteLength}return ea.isArrayBufferView(t)||ea.isArrayBuffer(t)?t.byteLength:(ea.isURLSearchParams(t)&&(t+=""),ea.isString(t)?(yield Za(t)).byteLength:void 0)})))(r):a})),sn={http:null,xhr:$a,fetch:Xa&&(t=>u(e,null,(function*(){let{url:e,method:r,data:a,signal:n,cancelToken:o,timeout:s,onDownloadProgress:i,onUploadProgress:l,responseType:u,headers:p,withCredentials:m="same-origin",fetchOptions:f}=Va(t);u=u?(u+"").toLowerCase():"text";let g,_=Ja([n,o&&o.toAbortSignal()],s);const v=_&&_.unsubscribe&&(()=>{_.unsubscribe()});let h;try{if(l&&tn&&"get"!==r&&"head"!==r&&0!==(h=yield on(p,a))){let t,r=new Request(e,{method:"POST",body:a,duplex:"half"});if(ea.isFormData(a)&&(t=r.headers.get("content-type"))&&p.setContentType(t),r.body){const[e,t]=Da(h,La(Ua(l)));a=Ya(r.body,65536,e,t)}}ea.isString(m)||(m=m?"include":"omit");const n="credentials"in Request.prototype;g=new Request(e,d(c({},f),{signal:_,method:r.toUpperCase(),headers:p.normalize().toJSON(),body:a,duplex:"half",credentials:n?m:void 0}));let o=yield fetch(g);const s=rn&&("stream"===u||"response"===u);if(rn&&(i||s&&v)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=ea.toFiniteNumber(o.headers.get("content-length")),[r,a]=i&&Da(t,La(Ua(i),!0))||[];o=new Response(Ya(o.body,65536,r,(()=>{a&&a(),v&&v()})),e)}u=u||"text";let y=yield an[ea.findKey(an,u)||"text"](o,t);return!s&&v&&v(),yield new Promise(((e,r)=>{Fa(e,r,{data:y,headers:Aa.from(o.headers),status:o.status,statusText:o.statusText,config:t,request:g})}))}catch(y){if(v&&v(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new ta("Network Error",ta.ERR_NETWORK,t,g),{cause:y.cause||y});throw ta.from(y,y&&y.code,t,g)}})))};ea.forEach(sn,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(r){}Object.defineProperty(e,"adapterName",{value:t})}}));const ln=e=>`- ${e}`,cn=e=>ea.isFunction(e)||null===e||!1===e,dn=e=>{e=ea.isArray(e)?e:[e];const{length:t}=e;let r,a;const n={};for(let o=0;o<t;o++){let t;if(r=e[o],a=r,!cn(r)&&(a=sn[(t=String(r)).toLowerCase()],void 0===a))throw new ta(`Unknown adapter '${t}'`);if(a)break;n[t||"#"+o]=a}if(!a){const e=Object.entries(n).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new ta("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(ln).join("\n"):" "+ln(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return a};function un(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Na(null,e)}function pn(e){return un(e),e.headers=Aa.from(e.headers),e.data=Ia.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),dn(e.adapter||Ea.adapter)(e).then((function(t){return un(e),t.data=Ia.call(e,e.transformResponse,t),t.headers=Aa.from(t.headers),t}),(function(t){return ja(t)||(un(e),t&&t.response&&(t.response.data=Ia.call(e,e.transformResponse,t.response),t.response.headers=Aa.from(t.response.headers))),Promise.reject(t)}))}const mn="1.9.0",fn={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{fn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));const gn={};fn.transitional=function(e,t,r){return(a,n,o)=>{if(!1===e)throw new ta(function(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(r?". "+r:"")}(n," has been removed"+(t?" in "+t:"")),ta.ERR_DEPRECATED);return t&&!gn[n]&&(gn[n]=!0),!e||e(a,n,o)}},fn.spelling=function(e){return(e,t)=>!0};const _n={assertOptions:function(e,t,r){if("object"!=typeof e)throw new ta("options must be an object",ta.ERR_BAD_OPTION_VALUE);const a=Object.keys(e);let n=a.length;for(;n-- >0;){const o=a[n],s=t[o];if(s){const t=e[o],r=void 0===t||s(t,o,e);if(!0!==r)throw new ta("option "+o+" must be "+r,ta.ERR_BAD_OPTION_VALUE)}else if(!0!==r)throw new ta("Unknown option "+o,ta.ERR_BAD_OPTION)}},validators:fn},vn=_n.validators;let hn=class{constructor(e){this.defaults=e||{},this.interceptors={request:new fa,response:new fa}}request(e,t){return u(this,null,(function*(){try{return yield this._request(e,t)}catch(r){if(r instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{r.stack?t&&!String(r.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(r.stack+="\n"+t):r.stack=t}catch(a){}}throw r}}))}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Wa(this.defaults,t);const{transitional:r,paramsSerializer:a,headers:n}=t;void 0!==r&&_n.assertOptions(r,{silentJSONParsing:vn.transitional(vn.boolean),forcedJSONParsing:vn.transitional(vn.boolean),clarifyTimeoutError:vn.transitional(vn.boolean)},!1),null!=a&&(ea.isFunction(a)?t.paramsSerializer={serialize:a}:_n.assertOptions(a,{encode:vn.function,serialize:vn.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),_n.assertOptions(t,{baseUrl:vn.spelling("baseURL"),withXsrfToken:vn.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=n&&ea.merge(n.common,n[t.method]);n&&ea.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete n[e]})),t.headers=Aa.concat(o,n);const s=[];let i=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const l=[];let c;this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)}));let d,u=0;if(!i){const e=[pn.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,l),d=e.length,c=Promise.resolve(t);u<d;)c=c.then(e[u++],e[u++]);return c}d=s.length;let p=t;for(u=0;u<d;){const e=s[u++],t=s[u++];try{p=e(p)}catch(m){t.call(this,m);break}}try{c=pn.call(this,p)}catch(m){return Promise.reject(m)}for(u=0,d=l.length;u<d;)c=c.then(l[u++],l[u++]);return c}getUri(e){return ma(za((e=Wa(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};ea.forEach(["delete","get","head","options"],(function(e){hn.prototype[e]=function(t,r){return this.request(Wa(r||{},{method:e,url:t,data:(r||{}).data}))}})),ea.forEach(["post","put","patch"],(function(e){function t(t){return function(r,a,n){return this.request(Wa(n||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:a}))}}hn.prototype[e]=t(),hn.prototype[e+"Form"]=t(!0)}));const yn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(yn).forEach((([e,t])=>{yn[t]=e}));const bn=function e(t){const r=new hn(t),a=mr(hn.prototype.request,r);return ea.extend(a,hn.prototype,r,{allOwnKeys:!0}),ea.extend(a,r,null,{allOwnKeys:!0}),a.create=function(r){return e(Wa(t,r))},a}(Ea);bn.Axios=hn,bn.CanceledError=Na,bn.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const r=this;this.promise.then((e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null})),this.promise.then=e=>{let t;const a=new Promise((e=>{r.subscribe(e),t=e})).then(e);return a.cancel=function(){r.unsubscribe(t)},a},e((function(e,a,n){r.reason||(r.reason=new Na(e,a,n),t(r.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},bn.isCancel=ja,bn.VERSION=mn,bn.toFormData=la,bn.AxiosError=ta,bn.Cancel=bn.CanceledError,bn.all=function(e){return Promise.all(e)},bn.spread=function(e){return function(t){return e.apply(null,t)}},bn.isAxiosError=function(e){return ea.isObject(e)&&!0===e.isAxiosError},bn.mergeConfig=Wa,bn.AxiosHeaders=Aa,bn.formToJSON=e=>Sa(ea.isHTMLForm(e)?new FormData(e):e),bn.getAdapter=dn,bn.HttpStatusCode=yn,bn.default=bn;const{Axios:wn,AxiosError:xn,CanceledError:kn,isCancel:Sn,CancelToken:En,VERSION:Rn,all:Cn,Cancel:On,isAxiosError:Tn,spread:Pn,toFormData:An,AxiosHeaders:In,HttpStatusCode:jn,formToJSON:Nn,getAdapter:Fn,mergeConfig:Ln}=bn;function Dn(e,t){var r,a,n,o,s,i,l,c,d,u,p,m,f,g,_;try{if(!e)throw new Error("技术分析数据为空");if(function(e){return"object"==typeof e&&null!==e&&"status"in e&&"string"==typeof e.status}(e)&&"data"in e){if("success"!==e.status)throw new Error(`API响应错误: ${e.status}`);if(!e.data)throw new Error("API响应中data为空");e=e.data}if("object"==typeof(_=e)&&null!==_&&"trend_up_probability"in _&&"trend_sideways_probability"in _&&"trend_down_probability"in _)try{return{current_price:"number"==typeof e.current_price?e.current_price:0,snapshot_price:"number"==typeof e.snapshot_price?e.snapshot_price:0,trend_analysis:{probabilities:{up:"number"==typeof e.trend_up_probability?e.trend_up_probability:0,sideways:"number"==typeof e.trend_sideways_probability?e.trend_sideways_probability:0,down:"number"==typeof e.trend_down_probability?e.trend_down_probability:0},summary:"string"==typeof e.trend_summary?e.trend_summary:"无数据"},indicators_analysis:e.indicators_analysis||{},trading_advice:{action:"string"==typeof e.trading_action?e.trading_action:"无建议",reason:"string"==typeof e.trading_reason?e.trading_reason:"无数据",entry_price:"number"==typeof e.entry_price?e.entry_price:0,stop_loss:"number"==typeof e.stop_loss?e.stop_loss:0,take_profit:"number"==typeof e.take_profit?e.take_profit:0},risk_assessment:{level:"string"==typeof e.risk_level?e.risk_level:"中",score:"number"==typeof e.risk_score?e.risk_score:50,details:Array.isArray(e.risk_details)?e.risk_details:[]},last_update_time:"string"==typeof e.last_update_time?e.last_update_time:(new Date).toISOString()}}catch(v){throw new Error(`格式化强制刷新数据失败: ${v instanceof Error?v.message:String(v)}`)}if(function(e){return"object"==typeof e&&null!==e&&"trend_analysis"in e&&"indicators_analysis"in e&&"trading_advice"in e}(e))try{localStorage.getItem("i18n_debug");const t="number"==typeof e.price?e.price:0;let _=e.risk_assessment;return!_&&e.trading_advice&&(_={level:null!=(r=e.trading_advice.risk_level)?r:"中",score:null!=(a=e.trading_advice.risk_score)?a:50,details:Array.isArray(e.trading_advice.risk_details)?e.trading_advice.risk_details:[]}),{current_price:"number"==typeof e.current_price?e.current_price:t,snapshot_price:"number"==typeof e.snapshot_price?e.snapshot_price:t,trend_analysis:{probabilities:{up:"number"==typeof(null==(o=null==(n=e.trend_analysis)?void 0:n.probabilities)?void 0:o.up)?e.trend_analysis.probabilities.up:0,sideways:"number"==typeof(null==(i=null==(s=e.trend_analysis)?void 0:s.probabilities)?void 0:i.sideways)?e.trend_analysis.probabilities.sideways:0,down:"number"==typeof(null==(c=null==(l=e.trend_analysis)?void 0:l.probabilities)?void 0:c.down)?e.trend_analysis.probabilities.down:0},summary:"string"==typeof(null==(d=e.trend_analysis)?void 0:d.summary)?e.trend_analysis.summary:"无数据"},indicators_analysis:e.indicators_analysis||{},trading_advice:{action:"string"==typeof(null==(u=e.trading_advice)?void 0:u.action)?e.trading_advice.action:"无建议",reason:"string"==typeof(null==(p=e.trading_advice)?void 0:p.reason)?e.trading_advice.reason:"无数据",entry_price:"number"==typeof(null==(m=e.trading_advice)?void 0:m.entry_price)?e.trading_advice.entry_price:0,stop_loss:"number"==typeof(null==(f=e.trading_advice)?void 0:f.stop_loss)?e.trading_advice.stop_loss:0,take_profit:"number"==typeof(null==(g=e.trading_advice)?void 0:g.take_profit)?e.trading_advice.take_profit:0},risk_assessment:{level:"string"==typeof(null==_?void 0:_.level)?_.level:"中",score:"number"==typeof(null==_?void 0:_.score)?_.score:50,details:Array.isArray(null==_?void 0:_.details)?_.details:[]},last_update_time:"string"==typeof e.last_update_time?e.last_update_time:"string"==typeof e.timestamp?e.timestamp:(new Date).toISOString()}}catch(v){throw new Error(`格式化技术分析数据失败: ${v instanceof Error?v.message:String(v)}`)}throw new Error("无法识别的数据格式")}catch(v){return{current_price:0,snapshot_price:0,trend_analysis:{probabilities:{up:.33,sideways:.34,down:.33},summary:"Data loading failed, please refresh and try again"},indicators_analysis:{RSI:{value:0,analysis:"Data loading failed",support_trend:"neutral"},MACD:{value:{line:0,signal:0,histogram:0},analysis:"Data loading failed",support_trend:"neutral"},BollingerBands:{value:{upper:0,middle:0,lower:0},analysis:"Data loading failed",support_trend:"neutral"},BIAS:{value:0,analysis:"Data loading failed",support_trend:"neutral"},PSY:{value:0,analysis:"Data loading failed",support_trend:"neutral"},DMI:{value:{plus_di:0,minus_di:0,adx:0},analysis:"Data loading failed",support_trend:"neutral"},VWAP:{value:0,analysis:"Data loading failed",support_trend:"neutral"},FundingRate:{value:0,analysis:"Data loading failed",support_trend:"neutral"},ExchangeNetflow:{value:0,analysis:"Data loading failed",support_trend:"neutral"},NUPL:{value:0,analysis:"Data loading failed",support_trend:"neutral"},MayerMultiple:{value:0,analysis:"Data loading failed",support_trend:"neutral"}},trading_advice:{action:"No advice",reason:"Data loading failed",entry_price:0,stop_loss:0,take_profit:0},risk_assessment:{level:"medium",score:50,details:["Data loading failed, unable to assess risk"]},last_update_time:(new Date).toISOString()}}}const Un=()=>{if("undefined"==typeof chrome||void 0===chrome.runtime||"function"!=typeof chrome.runtime.sendMessage)return!1;const e="chrome-extension:"===window.location.protocol||"moz-extension:"===window.location.protocol||"extension:"===window.location.protocol;let t=!1;try{t=!!chrome.runtime.id}catch(r){}return e||t},Mn=t=>u(e,null,(function*(){if(!Un())throw new Error("Not running in extension environment, cannot use proxy");return new Promise(((e,r)=>{const{url:a,method:n,headers:o,data:s,params:i}=t;let l=a||"";if(i&&Object.keys(i).length>0){const e=new URLSearchParams;Object.entries(i).forEach((([t,r])=>{null!=r&&e.append(t,String(r))}));const t=e.toString();t&&(l+=(l.includes("?")?"&":"?")+t)}const u=l.includes("force_refresh=true");let p=null;const m=u?12e4:3e4;p=window.setTimeout((()=>{r(new Error(`Request timed out (${m/1e3}s)`))}),m);let f=o||{};if(!f.Authorization){const e=localStorage.getItem("token");e&&(f=d(c({},f),{Authorization:e}))}const g="string"==typeof n?n.toUpperCase():"GET";if("undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage){try{chrome.runtime.id}catch(_){}chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:l,method:g,headers:f,body:s}},(t=>{if(null!==p&&clearTimeout(p),chrome.runtime.lastError)r(new Error(chrome.runtime.lastError.message));else{if(!t||!t.success){const e=new Error((null==t?void 0:t.error)||"Proxy request failed");return(null==t?void 0:t.errorDetail)&&(e.detail=t.errorDetail),void r(e)}try{e({data:t.data,status:t.status,statusText:t.statusText,headers:{},config:{}})}catch(a){e({data:t.data,status:200,statusText:"OK",headers:{},config:{}})}}}))}else r(new Error("Chrome extension API not available. Please run in Chrome extension environment."))}))})),Bn=bn.create({baseURL:"http://127.0.0.1:8000/api",timeout:6e4,headers:{"Content-Type":"application/json"}});let zn=[],qn=0;const Wn=()=>{const e=localStorage.getItem("token");if(!e)return!1;if(!e.startsWith("Token ")&&!e.startsWith("Bearer "))try{return localStorage.setItem("token",`Token ${e}`),!0}catch(t){return!1}return!(e.startsWith("Token ")&&e.replace("Token ","").length<5)},Vn=e=>!!e&&(e.includes("/auth/login")||e.includes("/auth/register")||e.includes("/auth/send-code")||e.includes("/auth/request-password-reset")||e.includes("/auth/reset-password-with-code")),$n=()=>u(e,null,(function*(){const e=Date.now(),t=e-6e4;if(zn=zn.filter((e=>e.timestamp>t)),zn.length>=30){const t=6e4-(e-zn[0].timestamp);if(t>0)return yield new Promise((e=>setTimeout(e,t))),void(yield $n())}const r=e-qn;if(r<2e3){const e=2e3-r;yield new Promise((t=>setTimeout(t,e)))}zn.push({timestamp:e,count:1}),qn=e})),Jn=(t,r=0)=>u(e,null,(function*(){var e,a,n,o,s;try{if(!Wn())throw new Error("Token validation failed");yield $n();const r=localStorage.getItem("token");return r&&(r.startsWith("Token ")||r.startsWith("Bearer ")?t.headers.Authorization=r:t.headers.Authorization=`Token ${r}`),t.headers["Cache-Control"]="no-cache",t.headers.Pragma="no-cache",(null==(e=t.params)?void 0:e.force_refresh)&&(t.timeout=12e4),Un()?Mn(t):yield Bn(t)}catch(i){if("ERR_FILE_NOT_FOUND"===i.code&&Un()&&(null==(n=null==(a=i.config)?void 0:a.url)?void 0:n.includes(chrome.runtime.getURL(""))))return chrome.runtime.sendMessage({type:"RELOAD_RESOURCES"}),yield new Promise((e=>setTimeout(e,1e3))),Jn(t,r+1);if("Token validation failed"===i.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(i);const e=(null==(o=t.params)?void 0:o.force_refresh)?6:3;if(r<e&&("ERR_NETWORK"===i.code||"ERR_FILE_NOT_FOUND"===i.code||"ECONNABORTED"===i.code||500===(null==(s=i.response)?void 0:s.status))){const e=2e3*Math.pow(2,r);return yield new Promise((t=>setTimeout(t,e))),Jn(t,r+1)}throw i}}));Bn.interceptors.request.use((t=>u(e,null,(function*(){try{if(Vn(t.url)||t.headers.Authorization)t.headers.Authorization;else{const e=localStorage.getItem("token");e&&(t.headers.Authorization=e)}if(!Vn(t.url)&&!Wn())return Promise.reject(new Error("Token validation failed"));if(!Vn(t.url)){const e=localStorage.getItem("token");e&&(e.startsWith("Token ")||e.startsWith("Bearer ")?t.headers.Authorization=e:t.headers.Authorization=`Token ${e}`)}return Un()?Mn(t):(yield $n(),t)}catch(e){return Promise.reject(e)}}))),(e=>Promise.reject(e))),Bn.interceptors.response.use((e=>e.data&&"object"==typeof e.data?"success"===e.data.status||"error"===e.data.status?e.data:{status:"success",data:e.data}:Promise.reject(new Error("Invalid data format"))),(t=>u(e,null,(function*(){var e,r;const a=t.config;if("Token validation failed"===t.message)return localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login",Promise.reject(t);if("ERR_NETWORK"===t.code&&!a._retry){a._retry=!0;try{return yield Jn(a)}catch(n){return Promise.reject(n)}}if(("ERR_FILE_NOT_FOUND"===t.code||500===(null==(e=t.response)?void 0:e.status))&&!a._retry){a._retry=!0;try{return yield Jn(a)}catch(n){return Promise.reject(n)}}return 401===(null==(r=t.response)?void 0:r.status)&&(localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login"),Promise.reject(t)}))));const Kn=t=>u(e,null,(function*(){try{const e="http://127.0.0.1:8000/api/auth/send-code/";return(yield bn.post(e,{email:t.email.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw e}})),Hn=t=>u(e,null,(function*(){try{const e="http://127.0.0.1:8000/api/auth/register/";return(yield bn.post(e,{email:t.email.trim(),password:t.password.trim(),code:t.code.trim(),invitation_code:t.invitation_code.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw e}})),Gn=t=>u(e,null,(function*(){try{const e="http://127.0.0.1:8000/api/auth/login/";return(yield bn.post(e,{email:t.email.trim(),password:t.password.trim()},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw e}})),Yn=t=>u(e,null,(function*(){try{return(yield Bn.post("/auth/reset-password-with-code/",{email:t.email.trim(),code:t.code.trim(),new_password:t.new_password.trim(),confirm_password:t.confirm_password.trim()})).data}catch(e){throw e}})),Xn=t=>u(e,null,(function*(){try{const e="http://127.0.0.1:8000/api/auth/change-password/",r=localStorage.getItem("token");if(!r)throw new Error("Not logged in, unable to change password");return(yield bn.post(e,{current_password:t.current_password.trim(),new_password:t.new_password.trim(),confirm_password:t.confirm_password.trim()},{headers:{"Content-Type":"application/json",Authorization:r}})).data}catch(e){throw e}}));let Qn={};const Zn=(t,r="crypto")=>u(e,null,(function*(){var e,a,n;let o="";if(!t||"string"!=typeof t||!t.trim())throw new Error("交易对无效，无法刷新报告");try{const i=t.toUpperCase();let l;"crypto"===r?(l=i.endsWith("USDT")?i:`${i}USDT`,o=`/crypto/get_report/${l}/`):(l=i,o=`/stock/get_report/${l}/`);const c={};c._t=Date.now();const d=`${o}`;if(Qn[d])throw new Error("请求正在进行中，请稍后再试");Qn[d]=!0;try{const e=(yield Jn({url:o,method:"GET",params:c,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})).data;let t=e;if(e&&Array.isArray(e.reports)&&e.reports.length>0&&(t=e.reports[0]),"object"==typeof e&&"status"in e){if("not_found"===e.status)return e;if("success"===e.status&&"data"in e)return Dn(t)}return Dn(t)}catch(s){if(404===(null==(e=s.response)?void 0:e.status))return{status:"not_found",message:(null==(n=null==(a=s.response)?void 0:a.data)?void 0:n.message)||"未找到交易对数据",needs_refresh:!0};if("ERR_NETWORK"===s.code)throw new Error("网络连接错误，请检查您的网络连接");if("ECONNABORTED"===s.code)throw new Error("请求超时，服务器响应时间过长，请稍后重试");throw s}}finally{o&&(Qn[`${o}`]=!1)}})),eo=()=>u(e,null,(function*(){try{if(Un()){const e=yield new Promise(((e,t)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/invitation-info/",method:"GET",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json"}}},(r=>{chrome.runtime.lastError?t(new Error(chrome.runtime.lastError.message)):r.success?e(r):t(new Error(r.error||"请求失败"))}))}));if(e&&e.success&&e.data)return e.data;throw new Error("Invalid response format")}return yield Bn.get("/auth/invitation-info/")}catch(e){throw e}})),to=()=>u(e,null,(function*(){try{return Un()?yield new Promise(((e,t)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/invitation-info/ranking/",method:"GET",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json"}}},(r=>{chrome.runtime.lastError?t(new Error(chrome.runtime.lastError.message)):r.success?e(r.data):t(new Error(r.error||"请求失败"))}))})):yield Bn.get("/auth/invitation-info/ranking/")}catch(e){throw e}})),ro=t=>u(e,null,(function*(){try{if(Un()){const e=yield new Promise(((e,r)=>{chrome.runtime.sendMessage({type:"PROXY_API_REQUEST",data:{url:"/auth/claim-temporary-invitation/",method:"POST",headers:{Authorization:localStorage.getItem("token"),Accept:"application/json","Content-Type":"application/json"},body:{temporary_invitation_uuid:t}}},(t=>{chrome.runtime.lastError?r(new Error(chrome.runtime.lastError.message)):t.success?e(t):r(new Error(t.error||"请求失败"))}))}));if(e&&e.success&&e.data)return e.data;throw new Error("Invalid response format")}return yield Bn.post("/auth/claim-temporary-invitation/",{temporary_invitation_uuid:t})}catch(e){throw e}})),ao=t=>u(e,null,(function*(){try{return yield Bn.post("/crypto/favorites/",{symbol:t.symbol,market_type:t.market_type,name:t.name,exchange:t.exchange,sector:t.sector})}catch(e){throw e}})),no=(t,r)=>u(e,null,(function*(){try{return yield Bn.delete("/crypto/favorites/",{data:{symbol:t,market_type:r}})}catch(e){throw e}})),oo=(t,r="crypto")=>u(e,null,(function*(){try{return yield Bn.get(`/crypto/favorites/status/${t}/`,{params:{market_type:r}})}catch(e){throw e}})),so={class:"space-y-6"},io={class:"text-center py-8"},lo={class:"text-slate-400 text-sm"},co=w({__name:"ChartSkeleton",props:{loadingText:{type:String,default:"Loading analysis data..."}},setup:e=>(t,r)=>(N(),P("div",so,[r[1]||(r[1]=A('<div class="relative" data-v-bd257e41><div class="p-6 rounded-2xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 shadow-xl" data-v-bd257e41><div class="text-center space-y-4" data-v-bd257e41><div class="space-y-1" data-v-bd257e41><div class="h-4 w-28 bg-slate-700/50 rounded animate-pulse mx-auto" data-v-bd257e41></div><div class="flex items-baseline justify-center space-x-2" data-v-bd257e41><div class="h-12 w-48 bg-slate-700/50 rounded animate-pulse" data-v-bd257e41></div><div class="h-6 w-8 bg-slate-700/50 rounded animate-pulse" data-v-bd257e41></div></div></div><div class="flex justify-center gap-3 pt-2" data-v-bd257e41><div class="h-10 w-32 bg-slate-700/50 rounded-xl animate-pulse" data-v-bd257e41></div><div class="h-10 w-28 bg-slate-700/50 rounded-xl animate-pulse" data-v-bd257e41></div></div></div></div></div><div class="flex items-center justify-between p-3 rounded-xl bg-slate-800/50 border border-slate-700/50" data-v-bd257e41><div class="flex items-center text-xs" data-v-bd257e41><div class="w-4 h-4 bg-slate-700/50 rounded-full animate-pulse mr-2" data-v-bd257e41></div><div class="h-3 w-24 bg-slate-700/50 rounded animate-pulse mr-2" data-v-bd257e41></div><div class="h-3 w-20 bg-slate-700/50 rounded animate-pulse" data-v-bd257e41></div></div><div class="w-8 h-8 bg-slate-700/50 rounded-lg animate-pulse" data-v-bd257e41></div></div><div data-v-bd257e41><div class="h-6 w-32 bg-slate-700/50 rounded animate-pulse mb-4" data-v-bd257e41></div></div>',3)),I("div",io,[r[0]||(r[0]=I("div",{class:"loading-dots flex space-x-2 justify-center mb-3"},[I("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"0ms"}}),I("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"150ms"}}),I("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{"animation-delay":"300ms"}})],-1)),I("p",lo,j(e.loadingText),1)])]))}),uo=(e,t)=>{const r=e.__vccOpts||e;for(const[a,n]of t)r[a]=n;return r},po=uo(co,[["__scopeId","data-v-bd257e41"]]),mo={class:"fixed top-0 w-full z-50 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800/50"},fo={class:"max-w-[375px] mx-auto"},go={class:"flex items-center justify-between h-12 px-4"},_o={class:"flex items-center space-x-2"},vo={class:"text-lg font-semibold text-white"},ho={key:0,class:"absolute top-full left-0 w-full bg-slate-900 border-b border-slate-700 shadow-xl z-[60]"},yo={class:"max-w-[375px] mx-auto p-4"},bo={class:"space-y-3"},wo=["onClick","disabled"],xo={class:"flex items-center space-x-3"},ko={key:0,class:"bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full"},So={key:0,class:"ri-check-line text-white"},Eo=w({__name:"MarketHeader",props:{modelValue:{},isSearchActive:{type:Boolean}},emits:["update:modelValue","change","search-click"],setup(e,{emit:t}){const{t:r}=pr(),a=e,n=t,o=_(!1),s=[{value:"crypto",label:"market.crypto",icon:"ri-currency-line"},{value:"stock",label:"market.stock",icon:"ri-line-chart-line"},{value:"china",label:"market.china",icon:"ri-bank-line",disabled:!0,comingSoon:!0}],i=()=>{const e=s.find((e=>e.value===a.modelValue));return e?r(e.label):""},l=()=>{o.value=!o.value};return(e,t)=>(N(),P("header",mo,[I("div",fo,[I("div",go,[I("div",_o,[I("h1",vo,j(i()),1),I("button",{onClick:l,class:L(["p-1 rounded-md hover:bg-gray-700/50 transition-colors duration-200",{"bg-gray-700/50":o.value}])},[I("i",{class:L(["ri-arrow-down-s-line text-lg text-gray-300 transition-transform duration-200",{"rotate-180":o.value}])},null,2)],2)]),I("button",{onClick:t[0]||(t[0]=t=>e.$emit("search-click")),class:L(["p-2 rounded-xl border transition-all duration-200 hover:scale-105",{"bg-blue-500/20 border-blue-400/50 text-blue-300":e.isSearchActive,"bg-blue-500/10 hover:bg-blue-500/20 border-blue-500/30 text-blue-400":!e.isSearchActive}])},t[2]||(t[2]=[I("i",{class:"ri-search-line text-lg"},null,-1)]),2)])]),o.value?(N(),P("div",ho,[I("div",yo,[I("div",bo,[(N(),P(k,null,D(s,(t=>I("button",{key:t.value,onClick:e=>(e=>{e.disabled||(o.value=!1,n("update:modelValue",e.value),n("change",e.value))})(t),disabled:t.disabled,class:L(["w-full flex items-center justify-between p-4 rounded-xl transition-all duration-200 border",{"bg-blue-600 border-blue-500 shadow-lg text-white":e.modelValue===t.value&&!t.disabled,"bg-slate-800 border-slate-700 hover:bg-slate-700 hover:border-slate-600":e.modelValue!==t.value&&!t.disabled,"opacity-50 cursor-not-allowed bg-slate-800 border-slate-700":t.disabled}])},[I("div",xo,[I("i",{class:L([t.icon,"text-lg",{"text-white":e.modelValue===t.value&&!t.disabled,"text-gray-300":e.modelValue!==t.value&&!t.disabled,"text-gray-500":t.disabled}])},null,2),I("span",{class:L(["font-medium",{"text-white":e.modelValue===t.value&&!t.disabled,"text-white":e.modelValue!==t.value&&!t.disabled,"text-gray-500":t.disabled}])},j(U(r)(t.label)),3),t.comingSoon?(N(),P("div",ko,j(U(r)("common.coming_soon")),1)):F("",!0)]),e.modelValue!==t.value||t.disabled?F("",!0):(N(),P("i",So))],10,wo))),64))])])])):F("",!0),o.value?(N(),P("div",{key:1,onClick:t[1]||(t[1]=e=>o.value=!1),class:"fixed inset-0 z-40 bg-black/20"})):F("",!0)]))}}),Ro=["disabled","title"],Co=w({__name:"FavoriteButton",props:{symbol:{},marketType:{},name:{}},emits:["favoriteChanged"],setup(e,{emit:t}){const{t:r}=pr(),a=e,n=t,o=_(!1),s=_(!1),i=h((()=>s.value)),l=()=>u(this,null,(function*(){if(!o.value){if(o.value=!0,d())try{const e={symbol:a.symbol,name:a.name||a.symbol,market_type:a.marketType,exchange:a.exchange,sector:a.sector};if(s.value)"success"===(yield no(a.symbol,a.marketType)).status&&(s.value=!1,n("favoriteChanged",!1));else{const t=yield ao(e);"success"!==t.status&&"info"!==t.status||(s.value=!0,n("favoriteChanged",!0))}}catch(e){yield c(a.symbol,a.marketType,!s.value),s.value=!s.value,n("favoriteChanged",s.value)}else yield c(a.symbol,a.marketType,!s.value),s.value=!s.value,n("favoriteChanged",s.value);o.value=!1}})),c=(e,t,r)=>u(this,null,(function*(){yield new Promise((e=>setTimeout(e,300)));const a=JSON.parse(localStorage.getItem("favorites")||"[]"),n=`${e}-${t}`;if(r)a.includes(n)||a.push(n);else{const e=a.indexOf(n);e>-1&&a.splice(e,1)}localStorage.setItem("favorites",JSON.stringify(a))})),d=()=>"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage&&chrome.runtime.id,p=()=>u(this,null,(function*(){if(d())try{const e=yield oo(a.symbol,a.marketType);if("success"===e.status)return void(s.value=e.data.is_favorite)}catch(r){}const e=JSON.parse(localStorage.getItem("favorites")||"[]"),t=`${a.symbol}-${a.marketType}`;s.value=e.includes(t)}));return p(),y((()=>[a.symbol,a.marketType]),(()=>{p()})),(e,t)=>(N(),P("button",{onClick:l,disabled:o.value,class:L(["flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",i.value?"bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30":"bg-gray-700/50 text-gray-400 hover:bg-gray-600/50 hover:text-gray-300",o.value?"opacity-50 cursor-not-allowed":"cursor-pointer"]),title:i.value?U(r)("favorites.remove"):U(r)("favorites.add")},[I("i",{class:L([o.value?"ri-loader-4-line animate-spin":i.value?"ri-star-fill":"ri-star-line","text-sm"])},null,2)],10,Ro))}}),Oo=uo(Co,[["__scopeId","data-v-d38971cd"]]),To={key:0,class:"chrome-extension-modal",style:{"z-index":"9999"}},Po={class:"bg-[#232a36] rounded-xl shadow-lg px-6 py-8 flex flex-col items-center w-80 border-2 border-blue-500"},Ao={key:0,class:"w-full text-center"},Io={class:"text-white text-lg font-semibold mb-1"},jo={class:"text-gray-400 text-sm"},No={key:1,class:"w-full text-center"},Fo={class:"text-white text-lg font-semibold mb-1"},Lo={class:"text-gray-400 text-sm"},Do={class:"text-xs text-gray-500 mt-2"},Uo=w({__name:"LoadingModal",props:{visible:{type:Boolean,default:!1},type:{default:"refresh"},customStages:{}},setup(e){const{t:t}=pr(),r=e;y((()=>r.visible),(e=>{}),{immediate:!0});const a=_(""),n=_("");let o=null;const s=()=>localStorage.getItem("language")||"en-US",i=(e,r)=>{const a=t(e);return a===e?r:a},l=[{title:()=>i("analysis.calculating_indicators","zh-CN"===s()?"正在获取市场数据":"en-US"===s()?"Fetching market data...":"ja-JP"===s()?"市場データ取得中...":"ko-KR"===s()?"시장 데이터 가져오는 중...":"Fetching market data..."),sub:()=>i("analysis.calculating_indicators","zh-CN"===s()?"获取最新价格和交易量数据":"en-US"===s()?"Getting latest price and volume data":"ja-JP"===s()?"最新の価格と出来高データを取得":"ko-KR"===s()?"최신 가격 및 거래량 데이터 가져오기":"Getting latest price and volume data")},{title:()=>i("analysis.analyzing_trends","zh-CN"===s()?"正在计算技术指标":"en-US"===s()?"Calculating technical indicators...":"ja-JP"===s()?"テクニカル指標計算中...":"ko-KR"===s()?"기술 지표 계산 중...":"Calculating technical indicators..."),sub:()=>i("analysis.analyzing_trends","zh-CN"===s()?"分析RSI、MACD、布林带等指标":"en-US"===s()?"Analyzing RSI, MACD, Bollinger Bands, etc.":"ja-JP"===s()?"RSI、MACD、ボリンジャーバンドなどを分析":"ko-KR"===s()?"RSI, MACD, 볼린저 밴드 등 분석":"Analyzing RSI, MACD, Bollinger Bands, etc.")},{title:()=>i("analysis.generating_advice","zh-CN"===s()?"正在分析市场趋势":"en-US"===s()?"Analyzing market trends...":"ja-JP"===s()?"市場トレンド分析中...":"ko-KR"===s()?"시장 트렌드 분석 중...":"Analyzing market trends..."),sub:()=>i("analysis.generating_advice","zh-CN"===s()?"评估上涨、下跌和盘整概率":"en-US"===s()?"Evaluating up, down, and sideways probabilities":"ja-JP"===s()?"上昇、下降、横ばいの確率を評価":"ko-KR"===s()?"상승, 하락, 횡보 확률 평가":"Evaluating up, down, and sideways probabilities")},{title:()=>i("analysis.finalizing_data","zh-CN"===s()?"正在生成交易建议":"en-US"===s()?"Generating trading advice...":"ja-JP"===s()?"取引アドバイス生成中...":"ko-KR"===s()?"거래 조언 생성 중...":"Generating trading advice..."),sub:()=>i("analysis.finalizing_data","zh-CN"===s()?"制定入场价、止损价和目标价":"en-US"===s()?"Setting entry, stop loss, and target prices":"ja-JP"===s()?"エントリー、ストップロス、ターゲット価格を設定":"ko-KR"===s()?"진입, 손절, 목표 가격 설정":"Setting entry, stop loss, and target prices")},{title:()=>i("analysis.preparing_analysis_report","zh-CN"===s()?"正在完成分析报告":"en-US"===s()?"Finalizing analysis report...":"ja-JP"===s()?"分析レポート完成中...":"ko-KR"===s()?"분석 보고서 완성 중...":"Finalizing analysis report..."),sub:()=>""}],c=[{title:()=>i("analysis.generating_new_report","zh-CN"===s()?"正在生成新的分析报告":"en-US"===s()?"Generating new analysis report...":"ja-JP"===s()?"新しい分析レポートを生成中...":"ko-KR"===s()?"새로운 분석 보고서 생성 중...":"Generating new analysis report..."),sub:()=>i("analysis.please_wait","zh-CN"===s()?"请耐心等待，这可能需要一些时间":"en-US"===s()?"Please wait, this may take some time":"ja-JP"===s()?"お待ちください、時間がかかる場合があります":"ko-KR"===s()?"잠시 기다려 주세요, 시간이 걸릴 수 있습니다":"Please wait, this may take some time")}],d=()=>{o&&(clearInterval(o),o=null)};return y((()=>r.visible),(e=>{e?(()=>{let e=l;"generate"===r.type?e=c:"custom"===r.type&&r.customStages&&(e=r.customStages.map((e=>({title:()=>e.title,sub:()=>e.sub||""}))));let t=0;a.value=e[0].title(),n.value=e[0].sub(),o&&clearInterval(o),e.length>1&&"refresh"===r.type&&(o=setInterval((()=>{t=(t+1)%e.length,a.value=e[t].title(),n.value=e[t].sub()}),5e3))})():d()})),C((()=>{d()})),(e,t)=>e.visible?(N(),P("div",To,[I("div",Po,[t[0]||(t[0]=I("div",{class:"flex items-center justify-center mb-4"},[I("svg",{class:"animate-spin h-10 w-10 text-blue-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[I("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),I("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"})])],-1)),"generate"===e.type?(N(),P("div",Ao,[I("div",Io,j(c[0].title()),1),I("div",jo,j(c[0].sub()),1)])):"refresh"===e.type?(N(),P("div",No,[I("div",Fo,j(a.value||"Loading..."),1),I("div",Lo,j(n.value||"Please wait"),1),I("div",Do," Debug: type="+j(e.type)+", text="+j(a.value)+", sub="+j(n.value),1)])):M(e.$slots,"default",{key:2},void 0,!0)])])):F("",!0)}}),Mo=uo(Uo,[["__scopeId","data-v-241c290f"]]),Bo={class:"w-full max-w-[375px] mx-auto space-y-4"},zo={class:"relative overflow-hidden rounded-2xl bg-gradient-to-br from-yellow-600/10 via-orange-600/10 to-red-600/10 p-6 backdrop-blur-sm border border-yellow-500/20"},qo={class:"relative text-center space-y-4"},Wo={class:"text-xl font-bold text-white"},Vo={class:"text-slate-300 text-sm leading-relaxed"},$o={class:"p-4 rounded-2xl bg-slate-800/40 border border-slate-700/50"},Jo=["disabled"],Ko=w({__name:"TokenNotFoundView",props:{symbol:{},marketType:{},isRefreshing:{type:Boolean}},emits:["refresh-success","refresh-error"],setup(e,{emit:t}){const{t:r}=pr(),{locale:a}=Ht(),n=_(localStorage.getItem("language")||"en-US"),o=(e,t,a)=>{const n=r(e,a);return n===e?t:n};y((()=>a.value),(e=>{n.value=e})),R((()=>{window.addEventListener("language-changed",(e=>{var t;const r=(null==(t=e.detail)?void 0:t.language)||localStorage.getItem("language")||"en-US";n.value=r})),window.addEventListener("force-refresh-i18n",(()=>{const e=localStorage.getItem("language")||"en-US";n.value=e}))}));const s=e,i=t,l=_(!1),c=_(!1),d=h((()=>s.symbol&&"string"==typeof s.symbol?"stock"===s.marketType||s.symbol.toUpperCase().endsWith("USDT")?s.symbol:`${s.symbol}/USDT`:"stock"===s.marketType?"Unknown":"Unknown/USDT")),p=()=>u(this,null,(function*(){if(!l.value){l.value=!0,c.value=!0;try{const e=("china"===s.marketType?"stock":s.marketType)||"crypto",t=yield Zn(s.symbol,e);if(!t||"not_found"===t.status)throw new Error("Failed to generate report, please try again later");i("refresh-success"),setTimeout((()=>{c.value=!1}),1e3)}catch(e){i("refresh-error",e),c.value=!1}finally{l.value=!1}}}));return C((()=>{c.value=!1,l.value=!1})),(e,t)=>(N(),P("div",Bo,[I("div",zo,[t[1]||(t[1]=I("div",{class:"absolute inset-0 bg-gradient-to-br from-yellow-500/5 via-orange-500/5 to-red-500/5"},null,-1)),I("div",qo,[t[0]||(t[0]=I("div",{class:"w-16 h-16 mx-auto bg-yellow-500/20 rounded-full flex items-center justify-center"},[I("i",{class:"ri-database-2-line text-3xl text-yellow-400"})],-1)),I("h2",Wo,j(o("tokenNotFound.title","zh-CN"===n.value?`${d.value} 数据未找到`:"en-US"===n.value?`${d.value} Data Not Found`:"ja-JP"===n.value?`${d.value} データが見つかりません`:"ko-KR"===n.value?`${d.value} 데이터를 찾을 수 없습니다`:`${d.value} Data Not Found`,{symbol:d.value})),1),I("p",Vo,j(o("tokenNotFound.description","zh-CN"===n.value?"该代币尚未在我们的数据库中，点击下方按钮获取最新数据":"en-US"===n.value?"This token is not yet in our database. Click the button below to get the latest data.":"ja-JP"===n.value?"このトークンはまだデータベースにありません。下のボタンをクリックして最新データを取得してください。":"ko-KR"===n.value?"이 토큰은 아직 데이터베이스에 없습니다. 아래 버튼을 클릭하여 최신 데이터를 가져오세요.":"This token is not yet in our database. Click the button below to get the latest data.")),1)])]),I("div",$o,[I("button",{class:L(["w-full px-6 py-3 bg-blue-500/15 hover:bg-blue-500/25 text-blue-400 rounded-xl font-medium transition-all duration-200 hover:scale-[1.02] border border-blue-500/30 flex items-center justify-center space-x-2",{"opacity-50 cursor-not-allowed":c.value}]),onClick:p,disabled:c.value},[I("i",{class:L(["ri-refresh-line text-lg",{"animate-spin":c.value}])},null,2),I("span",null,j(o("tokenNotFound.refreshButton","zh-CN"===n.value?"获取最新市场数据":"en-US"===n.value?"Get Latest Market Data":"ja-JP"===n.value?"最新の市場データを取得":"ko-KR"===n.value?"최신 시장 데이터 가져오기":"Get Latest Market Data")),1)],10,Jo)]),c.value?(N(),B(Mo,{key:0,visible:c.value,type:"generate",text:U(r)("tokenNotFound.loading")},null,8,["visible","text"])):F("",!0)]))}}),Ho={class:"min-h-screen bg-[#0F172A]"},Go={class:"relative max-w-[375px] mx-auto bg-[#0F172A] min-h-screen flex flex-col"},Yo={key:0,class:"flex-1 pt-12 pb-16 overflow-y-auto"},Xo={class:"px-4 space-y-6"},Qo={key:0,class:"mb-6"},Zo={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-5 shadow-2xl"},es={class:"relative mb-4"},ts=["placeholder"],rs={key:0,class:"space-y-2"},as={key:0,class:"text-center py-6"},ns={class:"text-slate-400 text-sm mt-3"},os={key:1,class:"text-center py-6"},ss={class:"text-slate-400 text-sm"},is={key:2,class:"max-h-64 overflow-y-auto space-y-2"},ls=["onClick"],cs={class:"flex items-center space-x-3"},ds={class:"text-left"},us={class:"font-semibold text-white"},ps={class:"text-sm text-slate-400"},ms={key:1,class:"mb-6"},fs={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-5 shadow-2xl"},gs={class:"flex items-center justify-between mb-4"},_s={class:"text-lg font-semibold text-white"},vs={class:"text-sm text-slate-400 bg-slate-700/50 px-2 py-1 rounded-lg"},hs={key:0,class:"text-center py-8"},ys={class:"text-slate-400 text-sm"},bs={key:1,class:"space-y-2 max-h-64 overflow-y-auto"},ws=["onClick"],xs={class:"flex items-center space-x-3"},ks={class:"text-left"},Ss={class:"font-semibold text-white"},Es={class:"text-sm text-slate-400"},Rs={class:"flex items-center space-x-2"},Cs={class:"text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded-md"},Os={key:2,class:"mb-6"},Ts={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-5 shadow-2xl"},Ps={class:"flex items-center justify-between mb-5"},As={class:"text-lg font-semibold text-white"},Is={class:"text-sm text-slate-400 bg-slate-700/50 px-2 py-1 rounded-lg"},js={class:"grid grid-cols-4 gap-3"},Ns=["onClick","disabled"],Fs={class:"text-sm font-bold text-center"},Ls={key:0,class:"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full border-2 border-slate-800"},Ds={class:"relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-indigo-600/10 border border-slate-700/50 shadow-2xl"},Us={class:"relative p-6"},Ms={class:"flex items-center justify-between mb-6"},Bs={class:"flex items-center space-x-3"},zs={class:"text-2xl font-bold text-white tracking-tight"},qs={class:"flex items-center space-x-2"},Ws=["title"],Vs=["title"],$s={class:"flex items-baseline space-x-3 mb-4"},Js={class:"text-3xl font-bold text-white"},Ks={class:"text-lg text-slate-400 uppercase"},Hs={class:"flex items-center justify-between"},Gs={class:"flex items-center text-sm text-slate-400"},Ys=["disabled"],Xs={key:1,class:"flex-1 flex items-center justify-center px-4 pt-16"},Qs={class:"text-center space-y-6"},Zs={class:"inline-flex items-center px-4 py-2 rounded-full bg-orange-500/20 border border-orange-500/40"},ei={class:"text-orange-400 text-sm font-medium"},ti={key:2,class:"px-4 space-y-6 pb-24"},ri={key:3,class:"px-4 pb-24"},ai={key:4,class:"px-4 space-y-6 pb-24"},ni={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-5 shadow-2xl"},oi={class:"flex justify-center gap-4"},si={class:"text-sm font-medium"},ii={class:"text-sm font-medium"},li={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl"},ci={class:"text-xl font-bold text-white mb-5 flex items-center"},di={class:"grid grid-cols-3 gap-4 mb-6"},ui={class:"text-center p-4 bg-green-500/10 border border-green-500/30 rounded-xl"},pi={class:"text-2xl font-bold text-green-400"},mi={class:"text-sm text-green-300 mt-1"},fi={class:"text-center p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-xl"},gi={class:"text-2xl font-bold text-yellow-400"},_i={class:"text-sm text-yellow-300 mt-1"},vi={class:"text-center p-4 bg-red-500/10 border border-red-500/30 rounded-xl"},hi={class:"text-2xl font-bold text-red-400"},yi={class:"text-sm text-red-300 mt-1"},bi={class:"bg-slate-700/30 border border-slate-600/50 rounded-xl p-4"},wi={class:"text-slate-300 leading-relaxed"},xi={class:"bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl"},ki={class:"text-xl font-bold text-white mb-5 flex items-center"},Si={class:"space-y-4"},Ei={class:"flex items-center justify-between p-4 bg-slate-700/30 border border-slate-600/50 rounded-xl"},Ri={class:"text-slate-400"},Ci={class:"font-semibold text-white"},Oi={class:"bg-slate-700/30 border border-slate-600/50 rounded-xl p-4"},Ti={class:"text-slate-400 text-sm mb-2"},Pi={class:"text-slate-300 leading-relaxed"},Ai={class:"sticky bottom-0 bg-[#0F172A]/95 backdrop-blur-md border-t border-slate-700/50"},Ii={class:"grid grid-cols-3 h-16"},ji={class:"text-xs mt-1 font-medium"},Ni={class:"text-xs mt-1"},Fi={class:"text-xs mt-1"},Li=w({__name:"HomeView",setup(t){const{t:r}=pr(),a=_("crypto"),n=_(""),o=_(null),s=_(!1),i=_(!1),l=_(null),c=_(!1),d=_(!1),p=_(!0),m=_(null),f=_(""),g=_([]),v=_(!1),b=_([]),w=_([{symbol:"BTC",display:"BTC"},{symbol:"ETH",display:"ETH"},{symbol:"SOL",display:"SOL"},{symbol:"BNB",display:"BNB"},{symbol:"XRP",display:"XRP"},{symbol:"ADA",display:"ADA"},{symbol:"DOGE",display:"DOGE"},{symbol:"AVAX",display:"AVAX"}]),x=_([{symbol:"AAPL",display:"AAPL"},{symbol:"GOOGL",display:"GOOGL"},{symbol:"MSFT",display:"MSFT"},{symbol:"AMZN",display:"AMZN"},{symbol:"TSLA",display:"TSLA"},{symbol:"META",display:"META"},{symbol:"NVDA",display:"NVDA"},{symbol:"NFLX",display:"NFLX"}]),S=h((()=>"crypto"===a.value?w.value:"stock"===a.value?x.value:[])),E=_(!0),C=e=>{m.value===e?m.value=null:m.value=e},T=e=>{if(a.value=e,m.value=null,"china"===e)return;const t="crypto"===e?"BTC":"AAPL";n.value=t,X(t)};let M;const J=()=>{clearTimeout(M),M=setTimeout((()=>u(this,null,(function*(){if(f.value.trim()){v.value=!0;try{yield new Promise((e=>setTimeout(e,500))),g.value=[]}catch(e){}finally{v.value=!1}}else g.value=[]}))),300)},K=e=>{},H=()=>{const e=`Check out the technical analysis for ${n.value}`,t=`https://twitter.com/intent/tweet?text=${encodeURIComponent(e)}`;window.open(t,"_blank")},G=()=>{Z.info("Save chart image feature coming soon!")},Y=()=>u(this,null,(function*(){if(E.value&&!d.value){d.value=!0,E.value=!1;try{yield X(n.value,!0),Z.success(r("analysis.refresh_success"))}catch(e){Z.error(r("analysis.refresh_error"))}finally{d.value=!1,setTimeout((()=>{E.value=!0}),3e4)}}})),X=(t,r=!1)=>u(this,null,(function*(){var n;if(t){s.value=!0,l.value=null,c.value=!1,p.value=!0;try{let n;const s="china"===a.value?"stock":a.value;n=r?yield Zn(t,s):yield((t,r=!1,a="crypto")=>u(e,null,(function*(){var e,n,o;if(!t||"string"!=typeof t||!t.trim())throw new Error("Invalid symbol provided");try{const e=t.toUpperCase();let n,o;"crypto"===a?(n=e.endsWith("USDT")?e:`${e}USDT`,o=`/crypto/technical-indicators/${n}/`):(n=e,o=`/stock/technical-indicators/${n}/`);const s=o,i={};r&&(i._t=Date.now());const l=`http://127.0.0.1:8000/api${s}`,c=localStorage.getItem("token"),d=c?c.startsWith("Token ")?c:`Token ${c}`:"",u=(yield bn.get(l,{params:i,headers:{"Content-Type":"application/json",Authorization:d}})).data;if("object"==typeof u&&"status"in u){if("not_found"===u.status)return u;if("success"===u.status&&"data"in u)return Dn(u.data)}return Dn(u)}catch(l){if(404===(null==(e=l.response)?void 0:e.status))return{status:"not_found",message:(null==(o=null==(n=l.response)?void 0:n.data)?void 0:o.message)||"Token data not found",needs_refresh:!0};if("ERR_NETWORK"===l.code)throw new Error("Network connection error, please check your network");throw l}})))(t,!1,s),n&&"not_found"!==n.status?(o.value=n,p.value=!1):(c.value=!0,p.value=!1)}catch(i){404===(null==(n=i.response)?void 0:n.status)?c.value=!0:l.value=i.message||"Failed to load data",p.value=!1}finally{s.value=!1}}}));return R((()=>u(this,null,(function*(){const e=localStorage.getItem("token"),t=localStorage.getItem("userInfo");e&&t?(n.value="BTC",yield X("BTC")):window.location.href="/login"})))),y(o,(e=>{e&&(p.value=!1)})),y([c,l],(([e,t])=>{(e||t)&&(p.value=!1)})),(e,t)=>{var s,l,u,_,h,y,w,x,R,M,Z;const ee=$("router-link");return N(),P("div",Ho,[I("div",Go,[O(Eo,{modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=e=>a.value=e),onChange:T,onSearchClick:t[1]||(t[1]=e=>C("search")),"is-search-active":"search"===m.value},null,8,["modelValue","is-search-active"]),"china"!==a.value?(N(),P("main",Yo,[I("div",Xo,["search"===m.value?(N(),P("div",Qo,[I("div",Zo,[I("div",es,[z(I("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>f.value=e),onInput:J,type:"text",placeholder:U(r)("search.placeholder"),class:"w-full bg-slate-900/50 border border-slate-600/50 rounded-xl px-4 py-3.5 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"},null,40,ts),[[q,f.value]]),t[6]||(t[6]=I("i",{class:"ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"},null,-1))]),f.value.trim()?(N(),P("div",rs,[v.value?(N(),P("div",as,[t[7]||(t[7]=I("i",{class:"ri-loader-4-line animate-spin text-blue-400 text-2xl"},null,-1)),I("p",ns,j(U(r)("search.searching")),1)])):0===g.value.length?(N(),P("div",os,[I("p",ss,j(U(r)("search.no_results")),1)])):(N(),P("div",is,[(N(!0),P(k,null,D(g.value,(e=>(N(),P("button",{key:e.symbol,onClick:t=>(e=>{n.value=e.symbol,m.value=null,f.value="",g.value=[],X(e.symbol)})(e),class:"w-full flex items-center justify-between p-4 bg-slate-700/40 hover:bg-slate-600/60 rounded-xl transition-all duration-200 border border-slate-600/30 hover:border-slate-500/50"},[I("div",cs,[I("div",ds,[I("div",us,j(e.symbol),1),I("div",ps,j(e.name),1)])]),t[8]||(t[8]=I("i",{class:"ri-arrow-right-line text-slate-400"},null,-1))],8,ls)))),128))]))])):F("",!0)])])):F("",!0),"favorites"===m.value?(N(),P("div",ms,[I("div",fs,[I("div",gs,[I("h3",_s,j(U(r)("favorites.my_favorites")),1),I("span",vs,j(b.value.length),1)]),0===b.value.length?(N(),P("div",hs,[t[9]||(t[9]=I("i",{class:"ri-star-line text-5xl text-slate-500 mb-4"},null,-1)),I("p",ys,j(U(r)("favorites.empty")),1)])):(N(),P("div",bs,[(N(!0),P(k,null,D(b.value,(e=>(N(),P("button",{key:e.symbol,onClick:t=>(e=>{n.value=e.symbol,m.value=null,X(e.symbol)})(e),class:"w-full flex items-center justify-between p-4 bg-slate-700/40 hover:bg-slate-600/60 rounded-xl transition-all duration-200 border border-slate-600/30 hover:border-slate-500/50"},[I("div",xs,[I("div",ks,[I("div",Ss,j(e.symbol),1),I("div",Es,j(e.name||e.symbol),1)])]),I("div",Rs,[I("span",Cs,j("crypto"===e.market_type?U(r)("market.crypto"):U(r)("market.stock")),1),t[10]||(t[10]=I("i",{class:"ri-arrow-right-line text-slate-400"},null,-1))])],8,ws)))),128))]))])])):F("",!0),"popular"===m.value?(N(),P("div",Os,[I("div",Ts,[I("div",Ps,[I("h3",As,j("crypto"===a.value?U(r)("popular.crypto_tokens"):U(r)("popular.stocks")),1),I("span",Is,j(S.value.length),1)]),I("div",js,[(N(!0),P(k,null,D(S.value,(e=>(N(),P("button",{key:e.symbol,onClick:t=>(e=>{e.symbol!==n.value&&(i.value=!0,n.value=e.symbol,m.value=null,X(e.symbol).finally((()=>{i.value=!1})))})(e),disabled:i.value,class:L(["relative p-3.5 rounded-xl border transition-all duration-200 hover:scale-105 active:scale-95",{"bg-blue-500/20 border-blue-400/50 text-blue-300 shadow-lg shadow-blue-500/20":e.symbol===n.value,"bg-slate-700/40 border-slate-600/50 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500/60":e.symbol!==n.value&&!i.value,"bg-slate-800/30 border-slate-700/30 text-slate-500 cursor-not-allowed":i.value}])},[I("div",Fs,j(e.display),1),e.symbol===n.value?(N(),P("div",Ls)):F("",!0)],10,Ns)))),128))])])])):F("",!0),I("div",Ds,[t[14]||(t[14]=I("div",{class:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-indigo-500/5"},null,-1)),I("div",Us,[I("div",Ms,[I("div",Bs,[I("h1",zs,j(n.value?n.value:U(r)("common.loading")),1),n.value?(N(),B(Oo,{key:0,symbol:n.value,"market-type":a.value,onFavoriteChanged:K,class:"scale-110"},null,8,["symbol","market-type"])):F("",!0)]),I("div",qs,[I("button",{onClick:t[3]||(t[3]=e=>C("favorites")),class:L(["p-2.5 rounded-xl border transition-all duration-200 hover:scale-105",{"bg-yellow-500/20 border-yellow-400/50 text-yellow-300":"favorites"===m.value,"bg-yellow-500/10 hover:bg-yellow-500/20 border-yellow-500/30 text-yellow-400":"favorites"!==m.value}]),title:U(r)("common.my_favorites")},t[11]||(t[11]=[I("i",{class:"ri-bookmark-line text-lg"},null,-1)]),10,Ws),"china"!==a.value?(N(),P("button",{key:0,onClick:t[4]||(t[4]=e=>C("popular")),class:L(["p-2.5 rounded-xl border transition-all duration-200 hover:scale-105",{"bg-green-500/20 border-green-400/50 text-green-300":"popular"===m.value,"bg-green-500/10 hover:bg-green-500/20 border-green-500/30 text-green-400":"popular"!==m.value}]),title:"crypto"===a.value?U(r)("common.popular_tokens"):U(r)("common.popular_stocks")},t[12]||(t[12]=[I("i",{class:"ri-fire-line text-lg"},null,-1)]),10,Vs)):F("",!0)])]),I("div",$s,[I("span",Js,j((re=null==(s=o.value)?void 0:s.current_price,re?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(re):"--")),1),I("span",Ks,j((a.value,"USD")),1)]),I("div",Hs,[I("div",Gs,[t[13]||(t[13]=I("i",{class:"ri-time-line mr-2"},null,-1)),I("span",null,j((te=null==(l=o.value)?void 0:l.last_update_time,te?new Date(te).toLocaleString():"--")),1)]),O(U(Q),{content:E.value?U(r)("analysis.refresh_report"):U(r)("analysis.refresh_report_too_soon"),placement:"top"},{default:W((()=>[I("button",{onClick:t[5]||(t[5]=e=>E.value&&Y()),disabled:!E.value||d.value,class:L(["p-2 rounded-xl transition-all duration-200 hover:scale-105",E.value?"bg-blue-500/15 text-blue-400 hover:bg-blue-500/25 border border-blue-500/30":"bg-slate-700/30 text-slate-500 cursor-not-allowed border border-slate-700/30"])},[I("i",{class:L(["ri-refresh-line text-lg",{"animate-spin":d.value}])},null,2)],10,Ys)])),_:1},8,["content"])])])])])])):F("",!0),"china"===a.value?(N(),P("div",Xs,[I("div",Qs,[t[16]||(t[16]=A('<div class="w-20 h-20 mx-auto bg-orange-500/20 rounded-full flex items-center justify-center"><i class="ri-tools-line text-3xl text-orange-400"></i></div><div class="space-y-3"><h3 class="text-lg font-bold text-white">A股市场</h3><p class="text-slate-400 text-sm leading-relaxed">该功能正在开发中，敬请期待</p></div>',2)),I("div",Zs,[t[15]||(t[15]=I("i",{class:"ri-time-line mr-2 text-orange-400"},null,-1)),I("span",ei,j(U(r)("common.coming_soon")),1)])])])):F("",!0),p.value?(N(),P("div",ti,[O(po,{loadingText:"Loading price data..."})])):c.value?(N(),P("div",ri,[O(Ko,{symbol:n.value,"market-type":a.value,"is-refreshing":d.value},null,8,["symbol","market-type","is-refreshing"])])):o.value?(N(),P("div",ai,[I("div",ni,[I("div",oi,[I("button",{onClick:H,class:"flex items-center gap-2 px-5 py-3 bg-blue-500/15 hover:bg-blue-500/25 text-blue-400 rounded-xl transition-all duration-200 hover:scale-105 border border-blue-500/30"},[t[17]||(t[17]=I("i",{class:"ri-twitter-fill text-lg"},null,-1)),I("span",si,j(U(r)("analysis.share_to_twitter")),1)]),I("button",{onClick:G,class:"flex items-center gap-2 px-5 py-3 bg-slate-600/20 hover:bg-slate-600/30 text-slate-300 rounded-xl transition-all duration-200 hover:scale-105 border border-slate-600/40"},[t[18]||(t[18]=I("i",{class:"ri-image-line text-lg"},null,-1)),I("span",ii,j(U(r)("analysis.save_image")),1)])])]),I("div",li,[I("h3",ci,[t[19]||(t[19]=I("i",{class:"ri-trending-up-line mr-3 text-blue-400"},null,-1)),V(" "+j(U(r)("analysis.trend_analysis")),1)]),I("div",di,[I("div",ui,[I("div",pi,j(Math.round(100*((null==(_=null==(u=o.value.trend_analysis)?void 0:u.probabilities)?void 0:_.up)||0)))+"% ",1),I("div",mi,j(U(r)("analysis.up_trend")),1)]),I("div",fi,[I("div",gi,j(Math.round(100*((null==(y=null==(h=o.value.trend_analysis)?void 0:h.probabilities)?void 0:y.sideways)||0)))+"% ",1),I("div",_i,j(U(r)("analysis.sideways_trend")),1)]),I("div",vi,[I("div",hi,j(Math.round(100*((null==(x=null==(w=o.value.trend_analysis)?void 0:w.probabilities)?void 0:x.down)||0)))+"% ",1),I("div",yi,j(U(r)("analysis.down_trend")),1)])]),I("div",bi,[I("p",wi,j(null==(R=o.value.trend_analysis)?void 0:R.summary),1)])]),I("div",xi,[I("h3",ki,[t[20]||(t[20]=I("i",{class:"ri-lightbulb-line mr-3 text-yellow-400"},null,-1)),V(" "+j(U(r)("analysis.trading_advice")),1)]),I("div",Si,[I("div",Ei,[I("span",Ri,j(U(r)("analysis.recommended_action")),1),I("span",Ci,j(null==(M=o.value.trading_advice)?void 0:M.action),1)]),I("div",Oi,[I("div",Ti,j(U(r)("analysis.reason")),1),I("p",Pi,j(null==(Z=o.value.trading_advice)?void 0:Z.reason),1)])])])])):F("",!0),I("nav",Ai,[I("div",Ii,[O(ee,{to:"/",class:"flex flex-col items-center justify-center text-blue-400 border-t-2 border-blue-400"},{default:W((()=>[t[21]||(t[21]=I("i",{class:"ri-line-chart-line text-xl"},null,-1)),I("span",ji,j(U(r)("nav.market")),1)])),_:1,__:[21]}),O(ee,{to:"/points",class:"flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors"},{default:W((()=>[t[22]||(t[22]=I("i",{class:"ri-coin-line text-xl"},null,-1)),I("span",Ni,j(U(r)("nav.points")),1)])),_:1,__:[22]}),O(ee,{to:"/profile",class:"flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors"},{default:W((()=>[t[23]||(t[23]=I("i",{class:"ri-settings-3-line text-xl"},null,-1)),I("span",Fi,j(U(r)("nav.settings")),1)])),_:1,__:[23]})])]),d.value?(N(),B(Mo,{key:5,visible:d.value,type:"refresh"},null,8,["visible"])):F("",!0)])]);var te,re}}}),Di={class:"container mx-auto px-4 py-8"},Ui={class:"max-w-4xl mx-auto"},Mi={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"},Bi={class:"card bg-dark-800 border border-dark-700"},zi={class:"space-y-4"},qi={class:"flex flex-wrap gap-2"},Wi={class:"flex flex-wrap gap-2"},Vi={class:"mb-12"},$i={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ji={class:"text-center"},Ki={class:"text-3xl mb-3"},Hi={class:"text-gray-400 text-sm"},Gi={class:"card bg-dark-800 border border-dark-700"},Yi={class:"grid grid-cols-2 md:grid-cols-4 gap-6 text-center"},Xi={class:"text-sm text-gray-400"},Qi=w({__name:"AboutView",setup(e){const t=["Vue 3","TypeScript","Vite","TailwindCSS","Element Plus","Vue Router","Pinia"],r=["ESLint","Prettier","PostCSS","Autoprefixer","Terser","Vue DevTools"],a=[{icon:"📊",title:"实时数据",description:"获取最新的市场数据和价格信息",color:"text-blue-400"},{icon:"📈",title:"技术分析",description:"专业的技术指标和图表分析",color:"text-green-400"},{icon:"🔔",title:"智能提醒",description:"价格预警和重要事件通知",color:"text-yellow-400"},{icon:"🌐",title:"多语言",description:"支持中文、英文、日文、韩文",color:"text-purple-400"},{icon:"📱",title:"响应式",description:"完美适配桌面端和移动端",color:"text-pink-400"},{icon:"🔒",title:"安全性",description:"数据加密和隐私保护",color:"text-red-400"}],n=[{label:"组件数量",value:"20+",color:"text-blue-400"},{label:"代码行数",value:"5K+",color:"text-green-400"},{label:"依赖包",value:"15+",color:"text-blue-400"},{label:"构建时间",value:"<30s",color:"text-yellow-400"}];return R((()=>{})),(e,o)=>(N(),P("div",Di,[I("div",Ui,[o[6]||(o[6]=I("div",{class:"text-center mb-12"},[I("h1",{class:"text-4xl font-bold mb-4 text-gray-200"}," 关于 CoolTrade "),I("p",{class:"text-xl text-gray-400"}," 了解我们的项目和技术架构 ")],-1)),I("div",Mi,[o[3]||(o[3]=A('<div class="card bg-dark-800 border border-dark-700"><h2 class="text-2xl font-semibold mb-4 text-blue-400"><i class="ri-information-line mr-2"></i> 项目简介 </h2><div class="space-y-4 text-gray-300"><p> CoolTrade 是一个专业的加密货币分析平台，致力于为用户提供实时、准确的市场分析和交易建议。 </p><p> 我们使用最新的前端技术栈构建了这个现代化的 Web 应用，确保用户体验的流畅性和数据的实时性。 </p><ul class="list-disc list-inside space-y-2 text-sm"><li>实时市场数据监控</li><li>专业技术分析工具</li><li>多语言支持</li><li>响应式设计</li></ul></div></div>',1)),I("div",Bi,[o[2]||(o[2]=I("h2",{class:"text-2xl font-semibold mb-4 text-green-400"},[I("i",{class:"ri-code-line mr-2"}),V(" 技术架构 ")],-1)),I("div",zi,[I("div",null,[o[0]||(o[0]=I("h3",{class:"font-semibold text-gray-200 mb-2"},"前端技术",-1)),I("div",qi,[(N(),P(k,null,D(t,(e=>I("span",{key:e,class:"px-3 py-1 bg-primary-600 text-white text-xs rounded-full"},j(e),1))),64))])]),I("div",null,[o[1]||(o[1]=I("h3",{class:"font-semibold text-gray-200 mb-2"},"开发工具",-1)),I("div",Wi,[(N(),P(k,null,D(r,(e=>I("span",{key:e,class:"px-3 py-1 bg-green-600 text-white text-xs rounded-full"},j(e),1))),64))])])])])]),I("div",Vi,[o[4]||(o[4]=I("h2",{class:"text-2xl font-bold text-center mb-8 text-gray-200"}," 核心功能 ",-1)),I("div",$i,[(N(),P(k,null,D(a,(e=>I("div",{key:e.title,class:"card bg-dark-800 border border-dark-700 hover:border-blue-500 transition-colors"},[I("div",Ji,[I("div",Ki,j(e.icon),1),I("h3",{class:L(["text-lg font-semibold mb-2",e.color])},j(e.title),3),I("p",Hi,j(e.description),1)])]))),64))])]),I("div",Gi,[o[5]||(o[5]=I("h2",{class:"text-2xl font-semibold mb-6 text-center text-gray-200"}," 项目统计 ",-1)),I("div",Yi,[(N(),P(k,null,D(n,(e=>I("div",{key:e.label},[I("div",{class:L(["text-2xl font-bold mb-1",e.color])},j(e.value),3),I("div",Xi,j(e.label),1)]))),64))])])])]))}}),Zi="/assets/icon128.D2N9qAIP.png",el={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},tl={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},rl={class:"max-w-[375px] mx-auto"},al={class:"flex items-center px-4 py-3"},nl={class:"text-lg font-semibold"},ol={class:"flex-1 pt-16 pb-16"},sl={class:"max-w-[375px] mx-auto px-4"},il={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},ll={for:"email",class:"block text-sm font-medium text-gray-300 mb-1"},cl=["placeholder"],dl={class:"flex justify-between items-center mb-1"},ul={for:"password",class:"block text-sm font-medium text-gray-300"},pl=["placeholder"],ml=["disabled"],fl={class:"mt-6 text-center"},gl={class:"text-sm text-gray-400"},_l=w({__name:"LoginView",setup(e){const{t:t}=pr(),r=J(),a=_(!1),n=_(void 0),o=_({email:"",password:""}),s=()=>{r.push("/register")},i=()=>{r.push("/forgot-password")},l=()=>u(this,null,(function*(){var e,s,i,l,c,d,u;a.value=!0,n.value=void 0;try{const a=yield Gn({email:o.value.email.trim(),password:o.value.password.trim()});"success"===a.status&&(null==(e=a.data)?void 0:e.token)?(localStorage.setItem("token",a.data.token),localStorage.setItem("userInfo",JSON.stringify(a.data.user)),r.push("/")):n.value=t("errors.login_failed_no_token")}catch(p){if(null==(i=null==(s=p.response)?void 0:s.data)?void 0:i.message)if("object"==typeof p.response.data.message){const e=Object.values(p.response.data.message).flat();e.length>0?n.value=e[0]:n.value=t("errors.login_failed_check_input")}else n.value=p.response.data.message;else(null==(c=null==(l=p.response)?void 0:l.data)?void 0:c.detail)?n.value=p.response.data.detail:401===(null==(d=p.response)?void 0:d.status)?n.value=t("errors.email_or_password_incorrect"):429===(null==(u=p.response)?void 0:u.status)?n.value=t("errors.too_many_attempts"):"ECONNABORTED"===p.code?n.value=t("errors.connection_timeout"):p.message.includes("Network Error")?n.value=t("errors.network_error"):n.value=t("errors.login_failed")}finally{a.value=!1}}));return(e,c)=>(N(),P("div",el,[I("header",tl,[I("div",rl,[I("div",al,[I("button",{onClick:c[0]||(c[0]=e=>U(r).push("/")),class:"mr-2"},c[3]||(c[3]=[I("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),I("h1",nl,j(U(t)("auth.login")),1)])])]),I("main",ol,[I("div",sl,[c[4]||(c[4]=I("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[I("img",{src:Zi,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),I("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),I("form",{onSubmit:K(l,["prevent"]),class:"space-y-6"},[n.value?(N(),P("div",il,j(n.value),1)):F("",!0),I("div",null,[I("label",ll,j(U(t)("auth.email")),1),z(I("input",{id:"email","onUpdate:modelValue":c[1]||(c[1]=e=>o.value.email=e),type:"email",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",placeholder:U(t)("auth.email_placeholder"),required:""},null,8,cl),[[q,o.value.email]])]),I("div",null,[I("div",dl,[I("label",ul,j(U(t)("auth.password")),1),I("a",{href:"#",onClick:K(i,["prevent"]),class:"text-xs text-primary hover:underline"},j(U(t)("auth.forgot_password")),1)]),z(I("input",{id:"password","onUpdate:modelValue":c[2]||(c[2]=e=>o.value.password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",placeholder:U(t)("auth.password_placeholder"),required:""},null,8,pl),[[q,o.value.password]])]),I("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:a.value},j(a.value?U(t)("common.loading"):U(t)("auth.login")),9,ml)],32),I("div",fl,[I("p",gl,[V(j(U(t)("auth.no_account"))+" ",1),I("a",{href:"#",onClick:K(s,["prevent"]),class:"text-primary hover:underline"},j(U(t)("auth.register_now")),1)])])])])]))}}),vl=(e,t)=>{if("chrome-extension:"===window.location.protocol||"moz-extension:"===window.location.protocol||"extension:"===window.location.protocol){let r=`#${e}`;if(t){const e=Object.entries(t).map((([e,t])=>`${encodeURIComponent(e)}=${encodeURIComponent(t)}`)).join("&");e&&(r+=`?${e}`)}window.location.href=r}else xu.push({path:e,query:t})},hl={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},yl={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},bl={class:"max-w-[375px] mx-auto"},wl={class:"flex items-center px-4 py-3"},xl={class:"text-lg font-semibold"},kl={class:"flex-1 pt-16 pb-16"},Sl={class:"max-w-[375px] mx-auto px-4"},El={class:"block text-sm font-medium text-gray-400 mb-1"},Rl=["placeholder"],Cl={key:0,class:"mt-1 text-sm text-red-500"},Ol={class:"block text-sm font-medium text-gray-400 mb-1"},Tl=["placeholder"],Pl={key:0,class:"mt-1 text-sm text-red-500"},Al={class:"flex gap-2"},Il={class:"flex-1"},jl={class:"block text-sm font-medium text-gray-400 mb-1"},Nl=["placeholder"],Fl={key:0,class:"mt-1 text-sm text-red-500"},Ll=["disabled"],Dl={class:"block text-sm font-medium text-gray-400 mb-1"},Ul=["placeholder"],Ml={key:0,class:"mt-1 text-sm text-red-500"},Bl=["disabled"],zl={class:"mt-6 text-center"},ql={class:"text-sm text-gray-400"},Wl=w({__name:"RegisterView",setup(e){const{t:t}=pr(),r=J(),a=()=>{var e;vl("/login",e?{redirect:e}:void 0)},n=_({email:"",password:"",code:"",invitation_code:""}),o=_(!1),s=_(!1),i=_(0),l=_({email:"",password:"",code:"",invitation_code:""}),c=_(""),d=_(null),p=_(null),m=_(null),f=_(null),g=()=>{l.value={email:"",password:"",code:"",invitation_code:""},c.value=""},v=e=>{const t={email:d,password:p,code:m,invitation_code:f}[e];t.value&&setTimeout((()=>{var e;null==(e=t.value)||e.focus()}),100)},h=()=>{localStorage.setItem("registerFormData",JSON.stringify({email:n.value.email,password:n.value.password,code:n.value.code}))},y=()=>{l.value.email="",c.value="",h()},b=()=>{l.value.password="",c.value="",h()},w=()=>{l.value.code="",c.value="",h()},x=()=>{l.value.invitation_code=""},k=()=>u(this,null,(function*(){var e,r,a,o,d;if(g(),!n.value.email)return l.value.email=t("errors.email_required"),void v("email");s.value=!0;try{const e=yield Kn({email:n.value.email.trim()});e&&"success"===e.status?((()=>{i.value=60;const e=setInterval((()=>{i.value--,i.value<=0&&clearInterval(e)}),1e3)})(),c.value=""):c.value=(null==e?void 0:e.message)||t("errors.send_code_failed")}catch(u){(null==(a=null==(r=null==(e=u.response)?void 0:e.data)?void 0:r.message)?void 0:a.email)?(l.value.email=u.response.data.message.email[0]||t("errors.invalid_email_format"),v("email")):(null==(d=null==(o=u.response)?void 0:o.data)?void 0:d.message)?c.value="string"==typeof u.response.data.message?u.response.data.message:t("errors.send_code_failed"):c.value=t("errors.send_code_failed")}finally{s.value=!1}})),S=()=>u(this,null,(function*(){var e,a;g();let s=!1;if(n.value.email||(l.value.email=t("errors.email_required"),s||(v("email"),s=!0)),n.value.password||(l.value.password=t("errors.password_required"),s||(v("password"),s=!0)),n.value.code||(l.value.code=t("errors.verification_code_required"),s||(v("code"),s=!0)),n.value.invitation_code||(l.value.invitation_code=t("errors.invitation_code_required"),s||(v("invitation_code"),s=!0)),!s){o.value=!0;try{const e={email:n.value.email.trim(),password:n.value.password.trim(),code:n.value.code.trim(),invitation_code:n.value.invitation_code.trim()},a=yield Hn(e);a&&"success"===a.status?(localStorage.removeItem("registerFormData"),r.push("/login")):c.value=(null==a?void 0:a.message)||t("errors.registration_failed")}catch(i){if(null==(a=null==(e=i.response)?void 0:e.data)?void 0:a.message){const e=i.response.data.message;if("object"==typeof e){const t={email:"email",password:"password",code:"code",invitation_code:"invitation_code"};let r=!1;Object.entries(e).forEach((([e,a])=>{const n=t[e];n?(l.value[n]=Array.isArray(a)?a[0]:a,r||(v(n),r=!0)):c.value=Array.isArray(a)?a[0]:a}))}else"string"==typeof e&&(c.value=e)}else c.value=t("errors.registration_failed")}finally{o.value=!1}}}));return R((()=>{(()=>{const e=localStorage.getItem("registerFormData");if(e){const{email:t,password:r,code:a}=JSON.parse(e);n.value.email=t,n.value.password=r,n.value.code=a}})()})),(e,c)=>(N(),P("div",hl,[I("header",yl,[I("div",bl,[I("div",wl,[I("button",{onClick:c[0]||(c[0]=e=>U(r).push("/login")),class:"mr-2"},c[5]||(c[5]=[I("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),I("h1",xl,j(U(t)("auth.register")),1)])])]),I("main",kl,[I("div",Sl,[c[6]||(c[6]=I("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[I("img",{src:Zi,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),I("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),I("form",{onSubmit:K(S,["prevent"]),class:"space-y-4"},[I("div",null,[I("label",El,j(U(t)("auth.email")),1),z(I("input",{type:"email","onUpdate:modelValue":c[1]||(c[1]=e=>n.value.email=e),onInput:y,required:"",ref_key:"emailInput",ref:d,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",l.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.email_placeholder")},null,42,Rl),[[q,n.value.email]]),l.value.email?(N(),P("p",Cl,j(l.value.email),1)):F("",!0)]),I("div",null,[I("label",Ol,j(U(t)("auth.password")),1),z(I("input",{type:"password","onUpdate:modelValue":c[2]||(c[2]=e=>n.value.password=e),onInput:b,required:"",ref_key:"passwordInput",ref:p,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",l.value.password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.password_placeholder")},null,42,Tl),[[q,n.value.password]]),l.value.password?(N(),P("p",Pl,j(l.value.password),1)):F("",!0)]),I("div",Al,[I("div",Il,[I("label",jl,j(U(t)("auth.verification_code")),1),z(I("input",{type:"text","onUpdate:modelValue":c[3]||(c[3]=e=>n.value.code=e),onInput:w,required:"",ref_key:"codeInput",ref:m,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",l.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.verification_code_placeholder")},null,42,Nl),[[q,n.value.code]]),l.value.code?(N(),P("p",Fl,j(l.value.code),1)):F("",!0)]),I("button",{type:"button",onClick:k,disabled:s.value||i.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},j(i.value>0?U(t)("auth.retry_in_seconds",{seconds:i.value}):s.value?U(t)("common.sending"):U(t)("auth.send_code")),9,Ll)]),I("div",null,[I("label",Dl,j(U(t)("auth.invitation_code")),1),z(I("input",{type:"text","onUpdate:modelValue":c[4]||(c[4]=e=>n.value.invitation_code=e),onInput:x,required:"",ref_key:"invitationCodeInput",ref:f,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",l.value.invitation_code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.invitation_code_placeholder")},null,42,Ul),[[q,n.value.invitation_code]]),l.value.invitation_code?(N(),P("p",Ml,j(l.value.invitation_code),1)):F("",!0)]),I("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:o.value},j(o.value?U(t)("common.registering"):U(t)("auth.register")),9,Bl)],32),I("div",zl,[I("p",ql,[V(j(U(t)("auth.have_account"))+" ",1),I("a",{href:"#",onClick:K(a,["prevent"]),class:"text-primary hover:underline"},j(U(t)("auth.login_now")),1)])])])])]))}}),Vl={},$l=function(e={}){const t=!ue(e.globalInjection)||e.globalInjection,a=new Map,[n,o]=function(e){const t=g(),r=t.run((()=>Bt(e)));if(null==r)throw Et(St);return[t,r]}(e),s=r(""),i={install(e,...r){return u(this,null,(function*(){if(e.__VUE_I18N_SYMBOL__=s,e.provide(e.__VUE_I18N_SYMBOL__,i),ge(r[0])){const e=r[0];i.__composerExtend=e.__composerExtend}let a=null;t&&(a=function(e,t){const r=Object.create(null);Gt.forEach((e=>{const a=Object.getOwnPropertyDescriptor(t,e);if(!a)throw Et(St);const n=b(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,n)})),e.config.globalProperties.$i18n=r,Yt.forEach((r=>{const a=Object.getOwnPropertyDescriptor(t,r);if(!a||!a.value)throw Et(St);Object.defineProperty(e.config.globalProperties,`$${r}`,a)}));const a=()=>{delete e.config.globalProperties.$i18n,Yt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(e,i.global)),__VUE_I18N_FULL_INSTALL__&&function(e,...t){const r=ge(t[0])?t[0]:{};(!ue(r.globalInstall)||r.globalInstall)&&([Jt.name,"I18nT"].forEach((t=>e.component(t,Jt))),[$t.name,"I18nN"].forEach((t=>e.component(t,$t))),[Vt.name,"I18nD"].forEach((t=>e.component(t,Vt))))}(e,...r);const n=e.unmount;e.unmount=()=>{a&&a(),i.dispose(),n()}}))},get global(){return o},dispose(){n.stop()},__instances:a,__getInstance:function(e){return a.get(e)||null},__setInstance:function(e,t){a.set(e,t)},__deleteInstance:function(e){a.delete(e)}};return i}({locale:(()=>{const e=localStorage.getItem("language");return e&&["zh-CN","en-US","ja-JP","ko-KR"].includes(e)?e:(localStorage.setItem("language","en-US"),"en-US")})(),fallbackLocale:"en-US",globalInjection:!0,missingWarn:!1,fallbackWarn:!1,messages:{"zh-CN":Qt,"en-US":Zt,"ja-JP":er,"ko-KR":tr},warnHtmlMessage:!1,escapeParameter:!0}),Jl=e=>{if(["zh-CN","en-US","ja-JP","ko-KR"].includes(e)){if((localStorage.getItem("language")||"en-US")===e)return;localStorage.setItem("language",e),$l.global.locale.value=e;try{(function(e,t){let r=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),a=(null==e?void 0:e.nonce)||(null==e?void 0:e.getAttribute("nonce"));r=Promise.allSettled(t.map((e=>{if((e=function(e){return"/"+e}(e))in Vl)return;Vl[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,a&&n.setAttribute("nonce",a),document.head.appendChild(n),t?new Promise(((t,r)=>{n.addEventListener("load",t),n.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then((t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)}))})((()=>Promise.resolve().then((()=>dr))),void 0).then((t=>{t.setLocale(e)}))}catch(t){}localStorage.getItem("token")&&Kl(e),window.dispatchEvent(new CustomEvent("language-changed",{detail:{language:e}})),setTimeout((()=>{window.dispatchEvent(new Event("force-refresh-i18n"))}),100)}},Kl=t=>u(e,null,(function*(){try{const r=localStorage.getItem("token");if(!r)return;if("chrome-extension:"===window.location.protocol){try{const e=localStorage.getItem("userInfo");if(e){const r=JSON.parse(e);r.language=t,localStorage.setItem("userInfo",JSON.stringify(r))}}catch(e){}return}const a="/api/auth/profile/";if((yield fetch(a,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify({language:t})})).ok)try{const e=localStorage.getItem("userInfo");if(e){const r=JSON.parse(e);r.language=t,localStorage.setItem("userInfo",JSON.stringify(r))}}catch(e){}}catch(r){}})),Hl={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-scroll"},Gl={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},Yl={class:"max-w-[375px] mx-auto"},Xl={class:"flex items-center h-12 px-4"},Ql=["textContent"],Zl={class:"flex-1 pt-16 pb-16"},ec={class:"max-w-[375px] mx-auto px-4"},tc={key:0,class:"bg-gray-800 rounded-lg p-6 mb-6"},rc={class:"text-center"},ac={class:"text-lg font-semibold mb-2"},nc={class:"text-gray-400 text-sm mb-4"},oc={class:"bg-gray-800 rounded-lg p-6 mb-6"},sc={class:"flex items-center space-x-4"},ic={class:"w-16 h-16 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-2xl font-bold overflow-hidden"},lc={class:"flex-1"},cc={class:"text-base font-semibold"},dc={class:"text-gray-500 text-xs mt-1"},uc=["textContent"],pc={class:"space-y-4"},mc=["textContent"],fc=["textContent"],gc={class:"ml-auto flex items-center"},_c={class:"text-gray-400 mr-2"},vc={key:0,class:"fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4"},hc={class:"bg-gray-900 rounded-lg w-full max-w-sm overflow-hidden"},yc={class:"p-4 border-b border-gray-800 flex justify-between items-center"},bc={class:"text-lg font-medium"},wc={class:"p-4"},xc={class:"space-y-2"},kc=["onClick"],Sc={class:"flex items-center"},Ec={class:"text-lg mr-3"},Rc={key:0,class:"ri-check-line text-primary"},Cc={href:"https://www.cooltrade.xyz/privacy-policy/",target:"_blank",class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},Oc=["textContent"],Tc={class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},Pc=["textContent"],Ac=["textContent"],Ic={class:"fixed bottom-0 w-full bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},jc={class:"max-w-[375px] mx-auto"},Nc={class:"grid grid-cols-3 h-16"},Fc=["textContent"],Lc=["textContent"],Dc=["textContent"],Uc={class:"relative h-[600px] flex flex-col bg-[#0F172A]"},Mc={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800/50"},Bc={class:"max-w-[375px] mx-auto"},zc={class:"flex items-center h-12 px-4"},qc={class:"text-lg font-semibold"},Wc={class:"flex-1 pt-16 pb-16 overflow-y-auto"},Vc={class:"max-w-[375px] mx-auto px-4 space-y-6"},$c={class:"relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20 p-6 backdrop-blur-sm border border-white/10"},Jc={class:"relative"},Kc={class:"flex items-center justify-between mb-4"},Hc={class:"text-gray-400 text-sm"},Gc={class:"text-3xl font-bold text-white"},Yc={class:"text-right"},Xc={class:"text-gray-400 text-sm"},Qc={class:"text-xl font-semibold text-yellow-400"},Zc={class:"flex items-center text-sm text-gray-300"},ed={class:"rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-5"},td={class:"flex items-center justify-between mb-3"},rd={class:"flex items-center"},ad={class:"font-semibold text-white"},nd={class:"text-gray-400 text-sm"},od={class:"bg-gray-900/50 rounded-xl p-4 border border-gray-600/30"},sd={class:"flex items-center justify-between"},id={class:"text-gray-400 text-xs mb-1"},ld={class:"font-mono text-lg font-semibold text-white"},cd={class:"text-sm"},dd={class:"rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-5"},ud={class:"text-lg font-semibold text-white mb-4"},pd={key:0,class:"space-y-3"},md={class:"flex items-center space-x-3"},fd={class:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm font-bold text-white"},gd={class:"font-medium text-white text-sm"},_d={class:"text-gray-400 text-xs"},vd={class:"text-green-400 font-semibold text-sm"},hd={key:1,class:"text-center py-8"},yd={class:"text-gray-400 text-sm"},bd={class:"text-gray-500 text-xs mt-1"},wd={class:"sticky bottom-0 w-full z-20 bg-[#0F172A]/95 backdrop-blur-md border-t border-gray-800"},xd={class:"max-w-[375px] mx-auto"},kd={class:"grid grid-cols-3 h-16"},Sd={class:"text-xs mt-0.5"},Ed={class:"text-xs mt-0.5"},Rd={class:"text-xs mt-0.5"},Cd={key:0,class:"fixed bottom-24 left-0 right-0 flex justify-center z-30 px-4"},Od={class:"bg-green-500/90 backdrop-blur-sm text-white px-6 py-3 rounded-2xl shadow-lg border border-green-400/20 flex items-center space-x-2"},Td={class:"font-medium"},Pd="temporary_invitation_uuid",Ad={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},Id={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},jd={class:"max-w-[375px] mx-auto"},Nd={class:"flex items-center px-4 py-3"},Fd={class:"text-lg font-semibold"},Ld={class:"flex-1 pt-16 pb-16"},Dd={class:"max-w-[375px] mx-auto px-4"},Ud={class:"block text-sm font-medium text-gray-400 mb-1"},Md=["placeholder"],Bd={key:0,class:"mt-1 text-sm text-red-500"},zd={class:"flex gap-2"},qd={class:"flex-1"},Wd={class:"block text-sm font-medium text-gray-400 mb-1"},Vd=["placeholder"],$d={key:0,class:"mt-1 text-sm text-red-500"},Jd=["disabled"],Kd={class:"block text-sm font-medium text-gray-400 mb-1"},Hd=["placeholder"],Gd={key:0,class:"mt-1 text-sm text-red-500"},Yd={class:"mt-1 text-xs text-gray-500"},Xd={class:"block text-sm font-medium text-gray-400 mb-1"},Qd=["placeholder"],Zd={key:0,class:"mt-1 text-sm text-red-500"},eu=["disabled"],tu={class:"mt-6 text-center"},ru={class:"text-sm text-gray-400"},au={class:"min-h-screen flex flex-col bg-[#0F172A] overflow-y-auto"},nu={class:"fixed top-0 w-full z-10 bg-[#0F172A]/95 backdrop-blur-md border-b border-gray-800"},ou={class:"max-w-[375px] mx-auto"},su={class:"flex items-center px-4 py-3"},iu={class:"text-lg font-semibold"},lu={class:"flex-1 pt-16 pb-16"},cu={class:"max-w-[375px] mx-auto px-4"},du={key:0,class:"text-center py-8"},uu={class:"text-gray-400 mb-4"},pu={key:1},mu={class:"text-gray-400 mb-6"},fu={key:0,class:"p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm"},gu={key:1,class:"p-3 rounded-lg bg-green-500/10 border border-green-500/20 text-green-400 text-sm"},_u={for:"current_password",class:"block text-sm font-medium text-gray-300 mb-1"},vu={for:"new_password",class:"block text-sm font-medium text-gray-300 mb-1"},hu={class:"mt-1 text-xs text-gray-500"},yu={for:"confirm_password",class:"block text-sm font-medium text-gray-300 mb-1"},bu=["disabled"],wu=[{path:"/",name:"home",component:Li,meta:{title:"CoolTrade - Home",requiresAuth:!0}},{path:"/about",name:"about",component:Qi,meta:{title:"CoolTrade - About",requiresAuth:!1}},{path:"/login",name:"login",component:_l,meta:{title:"CoolTrade - Login",guest:!0}},{path:"/register",name:"register",component:Wl,meta:{title:"CoolTrade - Register",guest:!0}},{path:"/profile",name:"profile",component:w({__name:"ProfileView",setup(e){const t=J(),{t:r,locale:a}=pr(),n=_({id:0,email:"",created_at:"",updated_at:"",language:"zh-CN"}),o=_(!1),s=_(a.value);y(a,(e=>{s.value=e}));const i=()=>{const e=s.value,t=l.find((t=>t.code===e));return t?t.name:"Unknown"},l=[{code:"en-US",name:"English"},{code:"zh-CN",name:"简体中文"},{code:"ja-JP",name:"日本語"},{code:"ko-KR",name:"한국어"}],c=h((()=>!!localStorage.getItem("token"))),d=e=>{if(!e)return"";const t=new Date(e);let r="en-US";const a=localStorage.getItem("language");return"zh-CN"===a?r="zh-CN":"ja-JP"===a?r="ja-JP":"ko-KR"===a&&(r="ko-KR"),t.toLocaleDateString(r,{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},p=()=>u(this,null,(function*(){if(c.value)try{const t=localStorage.getItem("userInfo");if(t)try{const e=JSON.parse(t);n.value=e}catch(e){}if("chrome-extension:"===window.location.protocol)return;const r=(yield Bn.get("/auth/profile/")).data;"success"===(null==r?void 0:r.status)&&(null==r?void 0:r.data)&&(n.value=r.data,localStorage.setItem("userInfo",JSON.stringify(r.data)))}catch(t){}})),m=e=>u(this,null,(function*(){try{"chrome-extension:"===window.location.protocol||(yield Bn.put("/auth/profile/",{language:e})),n.value.language=e,localStorage.setItem("userInfo",JSON.stringify(n.value))}catch(t){n.value.language=e,localStorage.setItem("userInfo",JSON.stringify(n.value))}})),f=()=>{localStorage.removeItem("token"),localStorage.removeItem("userInfo"),t.push("/login")};return R((()=>u(this,null,(function*(){yield p();const e=localStorage.getItem("language")||"en-US";c.value&&n.value.language&&n.value.language!==e?Jl(n.value.language):s.value=e,(()=>{let e=localStorage.getItem("language")||"en-US",t=Date.now();window.addEventListener("language-changed",(r=>{var a;const n=(null==(a=r.detail)?void 0:a.language)||localStorage.getItem("language")||"en-US",o=Date.now();n===e&&o-t<1e3||(s.value=n,e=n,t=o)})),window.addEventListener("force-refresh-i18n",(()=>{const r=localStorage.getItem("language")||"en-US",a=Date.now();r===e&&a-t<1e3||(s.value=r,e=r,t=a)}))})()})))),(e,t)=>{var a,u;const p=$("router-link");return N(),P("div",Hl,[I("header",Gl,[I("div",Yl,[I("div",Xl,[I("h1",{class:"text-lg font-semibold",textContent:j(U(r)("nav.settings"))},null,8,Ql)])])]),I("main",Zl,[I("div",ec,[c.value?(N(),P(k,{key:1},[I("div",oc,[I("div",sc,[I("div",ic,j((null==(u=null==(a=n.value.email)?void 0:a[0])?void 0:u.toUpperCase())||"U"),1),I("div",lc,[I("h2",cc,j(n.value.email),1),I("p",dc,[I("span",{textContent:j(U(r)("profile.registration_time"))},null,8,uc),V(": "+j(d(n.value.created_at)),1)])])])]),I("div",pc,[O(p,{to:"/change-password",class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center"},{default:W((()=>[t[3]||(t[3]=I("i",{class:"ri-lock-password-line mr-3"},null,-1)),I("span",{textContent:j(U(r)("auth.change_password"))},null,8,mc),t[4]||(t[4]=I("i",{class:"ri-arrow-right-s-line ml-auto"},null,-1))])),_:1,__:[3,4]}),I("div",{class:"w-full py-3 px-4 bg-gray-800 text-white rounded-lg font-medium flex items-center cursor-pointer",onClick:t[0]||(t[0]=e=>o.value=!0)},[t[6]||(t[6]=I("i",{class:"ri-global-line mr-3"},null,-1)),I("span",{textContent:j(U(r)("profile.language_settings"))},null,8,fc),I("div",gc,[I("span",_c,j(i()),1),t[5]||(t[5]=I("i",{class:"ri-arrow-right-s-line"},null,-1))])]),o.value?(N(),P("div",vc,[I("div",hc,[I("div",yc,[I("h3",bc,j(U(r)("profile.language_settings")),1),I("button",{onClick:t[1]||(t[1]=e=>o.value=!1),class:"text-gray-400 hover:text-white"},t[7]||(t[7]=[I("i",{class:"ri-close-line text-xl"},null,-1)]))]),I("div",wc,[I("div",xc,[(N(),P(k,null,D(l,(e=>{return I("button",{key:e.code,onClick:t=>(e=>{s.value!==e?(o.value=!1,s.value=e,c.value&&m(e),Jl(e)):o.value=!1})(e.code),class:L(["w-full py-3 px-4 rounded-lg flex items-center justify-between transition-colors duration-200",s.value===e.code?"bg-primary/20 text-primary":"bg-gray-800 text-white hover:bg-gray-700"])},[I("div",Sc,[I("span",Ec,j((t=e.code,{"zh-CN":"🇨🇳","en-US":"🇺🇸","ja-JP":"🇯🇵","ko-KR":"🇰🇷"}[t]||"🌐")),1),I("span",null,j(e.name),1)]),s.value===e.code?(N(),P("i",Rc)):F("",!0)],10,kc);var t})),64))])])])])):F("",!0),I("a",Cc,[t[8]||(t[8]=I("i",{class:"ri-shield-check-line mr-3"},null,-1)),I("span",{textContent:j(U(r)("common.privacy_policy"))},null,8,Oc),t[9]||(t[9]=I("i",{class:"ri-external-link-line ml-auto"},null,-1))]),I("button",Tc,[t[10]||(t[10]=I("i",{class:"ri-information-line mr-3"},null,-1)),I("span",{textContent:j(U(r)("common.about_us"))},null,8,Pc),t[11]||(t[11]=I("i",{class:"ri-arrow-right-s-line ml-auto"},null,-1))]),I("button",{class:"w-full py-3 px-4 bg-red-500 text-white rounded-lg font-medium flex items-center",onClick:f},[t[12]||(t[12]=I("i",{class:"ri-logout-box-line mr-3"},null,-1)),I("span",{textContent:j(U(r)("auth.logout"))},null,8,Ac)])])],64)):(N(),P("div",tc,[I("div",rc,[t[2]||(t[2]=I("div",{class:"w-20 h-20 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center text-3xl font-bold mx-auto mb-4"},[I("i",{class:"ri-user-3-line"})],-1)),I("h2",ac,j(U(r)("auth.logout")),1),I("p",nc,j(U(r)("profile.profile")),1),O(p,{to:"/login",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:W((()=>[V(j(U(r)("auth.login")),1)])),_:1})])]))])]),I("nav",Ic,[I("div",jc,[I("div",Nc,[O(p,{to:"/",class:"flex flex-col items-center justify-center text-gray-500"},{default:W((()=>[t[13]||(t[13]=I("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",{class:"text-xs mt-0.5",textContent:j(U(r)("nav.market"))},null,8,Fc)])),_:1,__:[13]}),O(p,{to:"/points",class:"flex flex-col items-center justify-center text-gray-500"},{default:W((()=>[t[14]||(t[14]=I("i",{class:"ri-coin-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",{class:"text-xs mt-0.5",textContent:j(U(r)("nav.points"))},null,8,Lc)])),_:1,__:[14]}),O(p,{to:"/profile",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:W((()=>[t[15]||(t[15]=I("i",{class:"ri-settings-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",{class:"text-xs mt-0.5",textContent:j(U(r)("nav.settings"))},null,8,Dc)])),_:1,__:[15]})])])])])}}}),meta:{title:"CoolTrade - Profile",requiresAuth:!0}},{path:"/points",name:"points",component:w({__name:"PointsView",setup(e){const{t:t}=pr(),r=_({points:0,invitation_code:"",invitation_points_per_user:10,invitation_count:0,invitation_records:[]}),a=_(null),n=_(!1),o=()=>u(this,null,(function*(){if("undefined"!=typeof chrome&&chrome.runtime)try{const e="http://127.0.0.1:8000";chrome.runtime.sendMessage({type:"getCookie",data:{url:e,name:Pd}},(t=>{chrome.runtime.lastError||t&&t.error||t&&t.cookie&&(s(t.cookie.value),chrome.runtime.sendMessage({type:"removeCookie",data:{url:e,name:Pd}}))}))}catch(e){}})),s=e=>u(this,null,(function*(){try{if(!e)return;if(!localStorage.getItem("token"))return;const t=yield ro(e);"success"===t.status&&"Successfully claimed invitation and received reward"===t.message&&i()}catch(t){if("undefined"!=typeof chrome&&chrome.runtime)try{const e="http://127.0.0.1:8000";chrome.runtime.sendMessage({type:"removeCookie",data:{url:e,name:Pd}})}catch(r){}}})),i=()=>u(this,null,(function*(){try{if(!localStorage.getItem("token"))return;try{const e=yield eo();"success"===e.status&&e.data&&(r.value=e.data);const t=yield to();"success"===t.status&&void 0!==t.ranking&&(a.value=t.ranking)}catch(e){}}catch(e){}})),l=e=>{try{const t=new Date(e),r=(new Date).getTime()-t.getTime(),a=Math.floor(r/864e5);return 0===a?"Today":1===a?"Yesterday":a<7?`${a} days ago`:t.toLocaleDateString("en-US",{month:"short",day:"numeric"})}catch(t){return e}},c=()=>{if(!r.value.invitation_code)return;const e=t("points.share_invitation_text",{code:r.value.invitation_code,points:r.value.invitation_points_per_user})+`\nhttp://127.0.0.1:8000/?code=${r.value.invitation_code}`;navigator.clipboard.writeText(e).then((()=>{n.value=!0,setTimeout((()=>{n.value=!1}),2e3)})).catch((e=>{}))};return R((()=>{i(),o()})),(e,o)=>{const s=$("router-link");return N(),P("div",Uc,[I("header",Mc,[I("div",Bc,[I("div",zc,[I("h1",qc,j(U(t)("points.my_points")),1)])])]),I("main",Wc,[I("div",Vc,[I("div",$c,[o[1]||(o[1]=I("div",{class:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5"},null,-1)),I("div",Jc,[I("div",Kc,[I("div",null,[I("p",Hc,j(U(t)("points.total_points")),1),I("h2",Gc,j(r.value.points||0),1)]),I("div",Yc,[I("p",Xc,j(U(t)("points.ranking")),1),I("h3",Qc,"#"+j(a.value||"--"),1)])]),I("div",Zc,[o[0]||(o[0]=I("i",{class:"ri-trophy-line mr-2 text-yellow-400"},null,-1)),I("span",null,j(U(t)("points.total_invited",{count:r.value.invitation_count||0})),1)])])]),I("div",ed,[I("div",td,[I("div",rd,[o[2]||(o[2]=I("div",{class:"w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-3"},[I("i",{class:"ri-user-add-line text-lg text-white"})],-1)),I("div",null,[I("h3",ad,j(U(t)("points.invite_friends")),1),I("p",nd,"+"+j(r.value.invitation_points_per_user||10)+" "+j(U(t)("points.points")),1)])])]),I("div",od,[I("div",sd,[I("div",null,[I("p",id,j(U(t)("points.your_invitation_code")),1),I("p",ld,j(r.value.invitation_code||"..."),1)]),I("button",{onClick:c,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"},[o[3]||(o[3]=I("i",{class:"ri-file-copy-line"},null,-1)),I("span",cd,j(U(t)("points.share")),1)])])])]),I("div",dd,[I("h3",ud,j(U(t)("points.invitation_records")),1),r.value.invitation_records&&r.value.invitation_records.length>0?(N(),P("div",pd,[(N(!0),P(k,null,D(r.value.invitation_records,(e=>(N(),P("div",{key:e.invitee_email,class:"flex items-center justify-between p-3 rounded-xl bg-gray-900/30 border border-gray-600/20"},[I("div",md,[I("div",fd,j(e.invitee_email?e.invitee_email.charAt(0).toUpperCase():"?"),1),I("div",null,[I("p",gd,j(e.invitee_email),1),I("p",_d,j(l(e.created_at)),1)])]),I("div",vd,"+"+j(e.points_awarded),1)])))),128))])):(N(),P("div",hd,[o[4]||(o[4]=I("div",{class:"w-16 h-16 rounded-full bg-gray-700/50 flex items-center justify-center mx-auto mb-3"},[I("i",{class:"ri-user-add-line text-2xl text-gray-500"})],-1)),I("p",yd,j(U(t)("points.no_invitation_records")),1),I("p",bd,j(U(t)("points.invite_friends_to_earn_points")),1)]))])])]),I("nav",wd,[I("div",xd,[I("div",kd,[O(s,{to:"/",class:"flex flex-col items-center justify-center text-gray-500"},{default:W((()=>[o[5]||(o[5]=I("i",{class:"ri-line-chart-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",Sd,j(U(t)("nav.market")),1)])),_:1,__:[5]}),O(s,{to:"/points",class:"flex flex-col items-center justify-center text-primary border-t-2 border-primary"},{default:W((()=>[o[6]||(o[6]=I("i",{class:"ri-coin-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",Ed,j(U(t)("nav.points")),1)])),_:1,__:[6]}),O(s,{to:"/profile",class:"flex flex-col items-center justify-center text-gray-500"},{default:W((()=>[o[7]||(o[7]=I("i",{class:"ri-settings-3-line ri-lg w-6 h-6 flex items-center justify-center"},null,-1)),I("span",Rd,j(U(t)("nav.settings")),1)])),_:1,__:[7]})])])]),n.value?(N(),P("div",Cd,[I("div",Od,[o[8]||(o[8]=I("i",{class:"ri-check-line text-lg"},null,-1)),I("span",Td,j(U(t)("points.copy_success")),1)])])):F("",!0)])}}}),meta:{title:"CoolTrade - Points",requiresAuth:!0}},{path:"/forgot-password",name:"forgot-password",component:w({__name:"ForgotPasswordView",setup(e){const{t:t}=pr(),r=J(),a=_(!1),n=_(!1),o=_(0),s=_({email:"",code:"",new_password:"",confirm_password:""}),i=_({email:"",code:"",new_password:"",confirm_password:""}),l=_(null),c=_(null),d=_(null),p=_(null),m=()=>{r.push("/login")},f=()=>{i.value.email=""},g=()=>{i.value.code=""},v=()=>{i.value.new_password=""},h=()=>{i.value.confirm_password=""},y=()=>u(this,null,(function*(){var e,r,a;if(!s.value.email)return i.value.email=t("errors.email_required"),void(null==(e=l.value)||e.focus());n.value=!0;try{yield Kn({email:s.value.email.trim()}),(()=>{o.value=60;const e=setInterval((()=>{o.value--,o.value<=0&&clearInterval(e)}),1e3)})()}catch(c){if(null==(a=null==(r=c.response)?void 0:r.data)?void 0:a.message)if("object"==typeof c.response.data.message){const e=Object.values(c.response.data.message).flat();e.length>0&&(i.value.email=e[0])}else i.value.email=c.response.data.message}finally{n.value=!1}})),b=()=>u(this,null,(function*(){var e,t;a.value=!0,Object.keys(i.value).forEach((e=>{i.value[e]=""}));try{"success"===(yield Yn({email:s.value.email.trim(),code:s.value.code.trim(),new_password:s.value.new_password.trim(),confirm_password:s.value.confirm_password.trim()})).status&&r.push("/login")}catch(n){(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)&&"object"==typeof n.response.data.message&&Object.entries(n.response.data.message).forEach((([e,t])=>{e in i.value&&(i.value[e]=Array.isArray(t)?t[0]:t)}))}finally{a.value=!1}}));return(e,u)=>(N(),P("div",Ad,[I("header",Id,[I("div",jd,[I("div",Nd,[I("button",{onClick:u[0]||(u[0]=e=>U(r).push("/login")),class:"mr-2"},u[5]||(u[5]=[I("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),I("h1",Fd,j(U(t)("auth.forgot_password")),1)])])]),I("main",Ld,[I("div",Dd,[u[6]||(u[6]=I("div",{class:"flex flex-col items-center justify-center mt-8 mb-6"},[I("img",{src:Zi,alt:"Cooltrade Logo",class:"w-16 h-16 mb-2 rounded-lg shadow-lg"}),I("div",{class:"text-2xl font-bold text-white tracking-wide mb-1"},"Cooltrade")],-1)),I("form",{onSubmit:K(b,["prevent"]),class:"space-y-4"},[I("div",null,[I("label",Ud,j(U(t)("auth.email")),1),z(I("input",{type:"email","onUpdate:modelValue":u[1]||(u[1]=e=>s.value.email=e),onInput:f,required:"",ref_key:"emailInput",ref:l,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.email?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.email_placeholder")},null,42,Md),[[q,s.value.email]]),i.value.email?(N(),P("p",Bd,j(i.value.email),1)):F("",!0)]),I("div",zd,[I("div",qd,[I("label",Wd,j(U(t)("auth.verification_code")),1),z(I("input",{type:"text","onUpdate:modelValue":u[2]||(u[2]=e=>s.value.code=e),onInput:g,required:"",ref_key:"codeInput",ref:c,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.code?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.verification_code_placeholder")},null,42,Vd),[[q,s.value.code]]),i.value.code?(N(),P("p",$d,j(i.value.code),1)):F("",!0)]),I("button",{type:"button",onClick:y,disabled:n.value||o.value>0,class:"mt-6 px-4 py-2 bg-gray-800 text-white rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},j(o.value>0?U(t)("auth.retry_in_seconds",{seconds:o.value}):n.value?U(t)("common.sending"):U(t)("auth.send_code")),9,Jd)]),I("div",null,[I("label",Kd,j(U(t)("auth.new_password")),1),z(I("input",{type:"password","onUpdate:modelValue":u[3]||(u[3]=e=>s.value.new_password=e),onInput:v,required:"",ref_key:"passwordInput",ref:d,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.new_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.new_password_placeholder")},null,42,Hd),[[q,s.value.new_password]]),i.value.new_password?(N(),P("p",Gd,j(i.value.new_password),1)):F("",!0),I("p",Yd,j(U(t)("auth.password_requirements")),1)]),I("div",null,[I("label",Xd,j(U(t)("auth.confirm_new_password")),1),z(I("input",{type:"password","onUpdate:modelValue":u[4]||(u[4]=e=>s.value.confirm_password=e),onInput:h,required:"",ref_key:"confirmPasswordInput",ref:p,class:L(["w-full px-4 py-2 rounded-lg bg-gray-800 border focus:ring-1 outline-none transition-colors",i.value.confirm_password?"border-red-500 focus:border-red-500 focus:ring-red-500":"border-gray-700 focus:border-primary focus:ring-primary"]),placeholder:U(t)("auth.confirm_new_password_placeholder")},null,42,Qd),[[q,s.value.confirm_password]]),i.value.confirm_password?(N(),P("p",Zd,j(i.value.confirm_password),1)):F("",!0)]),I("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:a.value},j(a.value?U(t)("common.submitting"):U(t)("auth.reset_password")),9,eu)],32),I("div",tu,[I("p",ru,[V(j(U(t)("auth.remember_password"))+" ",1),I("a",{href:"#",onClick:K(m,["prevent"]),class:"text-primary hover:underline"},j(U(t)("auth.login_now")),1)])])])])]))}}),meta:{title:"CoolTrade - Forgot Password",guest:!0}},{path:"/change-password",name:"change-password",component:w({__name:"ChangePasswordView",setup(e){const{t:t}=pr(),r=J(),a=_(!1),n=_(void 0),o=_(void 0),s=_({current_password:"",new_password:"",confirm_password:""}),i=h((()=>!!localStorage.getItem("token"))),l=()=>u(this,null,(function*(){var e,r,i,l,c;a.value=!0,n.value=void 0,o.value=void 0;try{"success"===(yield Xn({current_password:s.value.current_password.trim(),new_password:s.value.new_password.trim(),confirm_password:s.value.confirm_password.trim()})).status&&(o.value=t("auth.password_changed"),s.value={current_password:"",new_password:"",confirm_password:""})}catch(d){if(null==(r=null==(e=d.response)?void 0:e.data)?void 0:r.message)if("object"==typeof d.response.data.message){const e=Object.values(d.response.data.message).flat();e.length>0&&(n.value=e[0])}else n.value=d.response.data.message;else(null==(l=null==(i=d.response)?void 0:i.data)?void 0:l.detail)?n.value=d.response.data.detail:401===(null==(c=d.response)?void 0:c.status)?n.value=t("errors.unauthorized"):n.value=t("errors.unknown_error")}finally{a.value=!1}}));return(e,c)=>{const d=$("router-link");return N(),P("div",au,[I("header",nu,[I("div",ou,[I("div",su,[I("button",{onClick:c[0]||(c[0]=e=>U(r).push("/profile")),class:"mr-2"},c[4]||(c[4]=[I("i",{class:"ri-arrow-left-line ri-lg"},null,-1)])),I("h1",iu,j(U(t)("auth.change_password")),1)])])]),I("main",lu,[I("div",cu,[i.value?(N(),P("div",pu,[I("p",mu,j(U(t)("auth.enter_current_and_new_password")),1),I("form",{onSubmit:K(l,["prevent"]),class:"space-y-6"},[n.value?(N(),P("div",fu,j(n.value),1)):F("",!0),o.value?(N(),P("div",gu,j(o.value),1)):F("",!0),I("div",null,[I("label",_u,j(U(t)("auth.current_password")),1),z(I("input",{id:"current_password","onUpdate:modelValue":c[1]||(c[1]=e=>s.value.current_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[q,s.value.current_password]])]),I("div",null,[I("label",vu,j(U(t)("auth.new_password")),1),z(I("input",{id:"new_password","onUpdate:modelValue":c[2]||(c[2]=e=>s.value.new_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[q,s.value.new_password]]),I("p",hu,j(U(t)("auth.password_requirements")),1)]),I("div",null,[I("label",yu,j(U(t)("auth.confirm_new_password")),1),z(I("input",{id:"confirm_password","onUpdate:modelValue":c[3]||(c[3]=e=>s.value.confirm_password=e),type:"password",class:"w-full px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",required:""},null,512),[[q,s.value.confirm_password]])]),I("button",{type:"submit",class:"w-full py-3 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",disabled:a.value},j(a.value?U(t)("common.submitting"):U(t)("auth.change_password")),9,bu)],32)])):(N(),P("div",du,[I("p",uu,j(U(t)("auth.please_login_first")),1),O(d,{to:"/login",class:"inline-block py-2 px-6 bg-gradient-to-r from-primary to-blue-500 text-white rounded-lg font-medium"},{default:W((()=>[V(j(U(t)("auth.login")),1)])),_:1})]))])])])}}}),meta:{title:"CoolTrade - Change Password",requiresAuth:!0}}],xu=H({history:G(),routes:wu});function ku(){const e=localStorage.getItem("token"),t=localStorage.getItem("userInfo");return e&&t}xu.beforeEach(((e,t,r)=>{var a;(null==(a=e.meta)?void 0:a.title)&&(document.title=e.meta.title);const n=e.matched.some((e=>e.meta.requiresAuth)),o=e.matched.some((e=>e.meta.guest));n&&!ku()?r({path:"/login",query:{redirect:e.fullPath}}):o&&ku()?r({path:"/"}):r()}));const Su={id:"app",class:"min-h-screen bg-dark-900 text-white"},Eu={class:"min-h-screen"},Ru=w({__name:"App",setup:e=>(R((()=>{})),(e,t)=>{const r=$("router-view");return N(),P("div",Su,[I("main",Eu,[O(r)])])})}),Cu=localStorage.getItem("language");"zh-CN"!==Cu&&Cu||localStorage.setItem("language","en-US");const Ou=Y(Ru);Ou.use(X()),Ou.use(xu),Ou.use(ee),Ou.use($l),Ou.use(lr),Ou.mount("#app")}},function(){return re||(0,te[a(te)[0]])((re={exports:{}}).exports,re),re.exports});export default ae();
