@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  letter-spacing: -0.011em;
}

#app {
  min-height: 100vh;
}

/* 自定义组件样式 */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer;
}

.card {
  @apply bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700;
}

/* 表单样式 */
.form-input {
  @apply w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
}

.form-label {
  @apply block text-sm font-medium text-gray-300 mb-2;
}

.form-group {
  @apply mb-4;
}

/* 菜单项样式 */
.menu-item {
  @apply flex items-center justify-between w-full px-4 py-3 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors duration-200 cursor-pointer border border-gray-700;
}

.menu-item-icon {
  @apply w-5 h-5 mr-3 text-gray-400;
}

.menu-item-text {
  @apply flex-1 text-left text-gray-200;
}

.menu-item-arrow {
  @apply w-4 h-4 text-gray-400;
}

/* 用户头像样式 */
.user-avatar {
  @apply w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg;
}

/* 暗色主题 */
.dark {
  @apply bg-gray-900 text-white;
}
