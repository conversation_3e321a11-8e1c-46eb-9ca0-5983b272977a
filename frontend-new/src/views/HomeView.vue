<template>
  <div class="min-h-screen bg-[#0F172A]">
    <!-- 主容器 -->
    <div class="relative max-w-[375px] mx-auto bg-[#0F172A] min-h-screen flex flex-col">

      <!-- 固定顶部导航 - 与PointsView保持同等高度 -->
      <MarketHeader
        v-model="currentMarketType"
        @change="handleMarketTypeChange"
        @search-click="togglePanel('search')"
        :is-search-active="activePanel === 'search'"
      />

      <!-- 主要内容区域 -->
      <main class="flex-1 pt-12 pb-16 overflow-y-auto relative" v-if="currentMarketType !== 'china'">
        <div class="px-4 space-y-6">

          <!-- 主要价格卡片 -->
          <div class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-indigo-600/10 border border-slate-700/50 shadow-2xl">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-indigo-500/5"></div>
            <div class="relative p-6">
              <!-- 资产标题行 -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                  <h1 class="text-2xl font-bold text-white tracking-tight">
                    {{ currentSymbol ? getDisplayTitle() : t('common.loading') }}
                  </h1>
                  <FavoriteButton
                    v-if="currentSymbol"
                    :symbol="currentSymbol"
                    :market-type="currentMarketType"
                    @favorite-changed="handleFavoriteChanged"
                    class="scale-110"
                  />
                </div>

                <!-- 快速操作按钮 -->
                <div class="flex items-center space-x-2">
                  <!-- 收藏按钮 -->
                  <button
                    @click="togglePanel('favorites')"
                    class="p-2.5 rounded-xl border transition-all duration-200 hover:scale-105"
                    :class="{
                      'bg-yellow-500/20 border-yellow-400/50 text-yellow-300': activePanel === 'favorites',
                      'bg-yellow-500/10 hover:bg-yellow-500/20 border-yellow-500/30 text-yellow-400': activePanel !== 'favorites'
                    }"
                    :title="t('common.my_favorites')"
                  >
                    <i class="ri-bookmark-line text-lg"></i>
                  </button>

                  <!-- 热门资产按钮 -->
                  <button
                    v-if="currentMarketType !== 'china'"
                    @click="togglePanel('popular')"
                    class="p-2.5 rounded-xl border transition-all duration-200 hover:scale-105"
                    :class="{
                      'bg-green-500/20 border-green-400/50 text-green-300': activePanel === 'popular',
                      'bg-green-500/10 hover:bg-green-500/20 border-green-500/30 text-green-400': activePanel !== 'popular'
                    }"
                    :title="currentMarketType === 'crypto' ? t('common.popular_tokens') : t('common.popular_stocks')"
                  >
                    <i class="ri-fire-line text-lg"></i>
                  </button>
                </div>
              </div>

              <!-- 价格信息 -->
              <div class="flex items-baseline space-x-3 mb-4">
                <span class="text-3xl font-bold text-white">
                  {{ formatPrice(analysisData?.current_price) }}
                </span>
                <span class="text-lg text-slate-400 uppercase">{{ currentMarketType === 'crypto' ? 'USD' : 'USD' }}</span>
              </div>

              <!-- 更新时间和刷新按钮 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center text-sm text-slate-400">
                  <i class="ri-time-line mr-2"></i>
                  <span>{{ formatTime(analysisData?.last_update_time) }}</span>
                </div>
                <el-tooltip
                  :content="!canRefreshReport ? t('analysis.refresh_report_too_soon') : t('analysis.refresh_report')"
                  placement="top"
                >
                  <button
                    @click="canRefreshReport && handleRefreshReport()"
                    :disabled="!canRefreshReport || isRefreshing"
                    class="p-2 rounded-xl transition-all duration-200 hover:scale-105"
                    :class="canRefreshReport
                      ? 'bg-blue-500/15 text-blue-400 hover:bg-blue-500/25 border border-blue-500/30'
                      : 'bg-slate-700/30 text-slate-500 cursor-not-allowed border border-slate-700/30'"
                  >
                    <i class="ri-refresh-line text-lg" :class="{ 'animate-spin': isRefreshing }"></i>
                  </button>
                </el-tooltip>
              </div>
            </div>
          </div>

        </div>

        <!-- 弹窗覆盖层 -->
        <div v-if="activePanel" class="absolute inset-0 z-50 bg-black/20 backdrop-blur-sm" @click="activePanel = null">
          <div class="p-4 pt-16">
            <!-- 搜索面板 -->
            <div v-if="activePanel === 'search'" @click.stop
                 class="bg-slate-800/95 backdrop-blur-md border border-slate-700/50 rounded-2xl p-5 shadow-2xl">
              <div class="relative mb-4">
                <input
                  v-model="searchQuery"
                  @input="handleSearch"
                  type="text"
                  :placeholder="t('search.placeholder')"
                  class="w-full bg-slate-900/50 border border-slate-600/50 rounded-xl px-4 py-3.5 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                />
                <i class="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
              </div>

              <!-- 搜索结果 -->
              <div v-if="searchQuery.trim()" class="space-y-2">
                <div v-if="searchLoading" class="text-center py-6">
                  <i class="ri-loader-4-line animate-spin text-blue-400 text-2xl"></i>
                  <p class="text-slate-400 text-sm mt-3">{{ t('search.searching') }}</p>
                </div>
                <div v-else-if="searchResults.length === 0" class="text-center py-6">
                  <p class="text-slate-400 text-sm">{{ t('search.no_results') }}</p>
                </div>
                <div v-else class="max-h-64 overflow-y-auto space-y-2">
                  <button
                    v-for="result in searchResults"
                    :key="result.symbol"
                    @click="selectSearchResult(result)"
                    class="w-full flex items-center justify-between p-4 bg-slate-700/40 hover:bg-slate-600/60 rounded-xl transition-all duration-200 border border-slate-600/30 hover:border-slate-500/50"
                  >
                    <div class="flex items-center space-x-3">
                      <div class="text-left">
                        <div class="font-semibold text-white">{{ result.symbol }}</div>
                        <div class="text-sm text-slate-400">{{ result.name }}</div>
                      </div>
                    </div>
                    <i class="ri-arrow-right-line text-slate-400"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 收藏面板 -->
            <div v-if="activePanel === 'favorites'" @click.stop
                 class="bg-slate-800/95 backdrop-blur-md border border-slate-700/50 rounded-2xl p-5 shadow-2xl">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">{{ t('favorites.my_favorites') }}</h3>
                <span class="text-sm text-slate-400 bg-slate-700/50 px-2 py-1 rounded-lg">{{ filteredFavorites.length }}</span>
              </div>

              <div v-if="filteredFavorites.length === 0" class="text-center py-8">
                <i class="ri-star-line text-5xl text-slate-500 mb-4"></i>
                <p class="text-slate-400 text-sm">{{ t('favorites.empty') }}</p>
              </div>
              <div v-else class="space-y-2 max-h-64 overflow-y-auto">
                <button
                  v-for="favorite in filteredFavorites"
                  :key="favorite.symbol"
                  @click="selectFavorite(favorite)"
                  class="w-full flex items-center justify-between p-4 bg-slate-700/40 hover:bg-slate-600/60 rounded-xl transition-all duration-200 border border-slate-600/30 hover:border-slate-500/50"
                >
                  <div class="flex items-center space-x-3">
                    <div class="text-left">
                      <div class="font-semibold text-white">{{ favorite.symbol }}</div>
                      <div class="text-sm text-slate-400">{{ favorite.name || favorite.symbol }}</div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded-md">
                      {{ favorite.market_type === 'crypto' ? t('market.crypto') : t('market.stock') }}
                    </span>
                    <i class="ri-arrow-right-line text-slate-400"></i>
                  </div>
                </button>
              </div>
            </div>

            <!-- 热门代币/股票面板 -->
            <div v-if="activePanel === 'popular'" @click.stop
                 class="bg-slate-800/95 backdrop-blur-md border border-slate-700/50 rounded-2xl p-5 shadow-2xl">
              <div class="flex items-center justify-between mb-5">
                <h3 class="text-lg font-semibold text-white">
                  {{ currentMarketType === 'crypto' ? t('popular.crypto_tokens') : t('popular.stocks') }}
                </h3>
                <span class="text-sm text-slate-400 bg-slate-700/50 px-2 py-1 rounded-lg">{{ currentPopularAssets.length }}</span>
              </div>

              <div class="grid grid-cols-4 gap-3">
                <button
                  v-for="asset in currentPopularAssets"
                  :key="asset.symbol"
                  @click="selectPopularAsset(asset)"
                  :disabled="analysisLoading"
                  class="relative p-3.5 rounded-xl border transition-all duration-200 hover:scale-105 active:scale-95"
                  :class="{
                    'bg-blue-500/20 border-blue-400/50 text-blue-300 shadow-lg shadow-blue-500/20': asset.symbol === currentSymbol,
                    'bg-slate-700/40 border-slate-600/50 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500/60': asset.symbol !== currentSymbol && !analysisLoading,
                    'bg-slate-800/30 border-slate-700/30 text-slate-500 cursor-not-allowed': analysisLoading
                  }"
                >
                  <div class="text-sm font-bold text-center">{{ asset.display }}</div>
                  <div
                    v-if="asset.symbol === currentSymbol"
                    class="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full border-2 border-slate-800"
                  ></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- A股开发中页面 -->
      <div class="flex-1 flex items-center justify-center px-4 pt-16" v-if="currentMarketType === 'china'">
        <div class="text-center space-y-6">
          <div class="w-20 h-20 mx-auto bg-orange-500/20 rounded-full flex items-center justify-center">
            <i class="ri-tools-line text-3xl text-orange-400"></i>
          </div>
          <div class="space-y-3">
            <h3 class="text-lg font-bold text-white">A股市场</h3>
            <p class="text-slate-400 text-sm leading-relaxed">该功能正在开发中，敬请期待</p>
          </div>
          <div class="inline-flex items-center px-4 py-2 rounded-full bg-orange-500/20 border border-orange-500/40">
            <i class="ri-time-line mr-2 text-orange-400"></i>
            <span class="text-orange-400 text-sm font-medium">{{ t('common.coming_soon') }}</span>
          </div>
        </div>
      </div>

      <!-- 骨架屏 -->
      <div v-if="showSkeleton" class="px-4 space-y-6 pb-24">
        <ChartSkeleton loadingText="Loading price data..." />
      </div>

      <!-- Token未找到视图 -->
      <div v-else-if="isTokenNotFound" class="px-4 pb-24">
        <TokenNotFoundView
          :symbol="currentSymbol"
          :market-type="currentMarketType"
          :is-refreshing="isRefreshing"
        />
      </div>

      <!-- 正常内容 - 有数据时显示 -->
      <div v-else-if="analysisData" class="px-4 space-y-4 pb-24">

        <!-- 操作按钮卡片 -->
        <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 shadow-2xl">
          <div class="flex justify-center gap-4">
            <button
              @click="shareToTwitter"
              class="flex items-center gap-2 px-5 py-3 bg-blue-500/15 hover:bg-blue-500/25 text-blue-400 rounded-xl transition-all duration-200 hover:scale-105 border border-blue-500/30"
            >
              <i class="ri-twitter-fill text-lg"></i>
              <span class="text-sm font-medium">{{ t('analysis.share_to_twitter') }}</span>
            </button>
            <button
              @click="saveChartImage"
              class="flex items-center gap-2 px-5 py-3 bg-slate-600/20 hover:bg-slate-600/30 text-slate-300 rounded-xl transition-all duration-200 hover:scale-105 border border-slate-600/40"
            >
              <i class="ri-image-line text-lg"></i>
              <span class="text-sm font-medium">{{ t('analysis.save_image') }}</span>
            </button>
          </div>
        </div>

        <!-- 趋势分析卡片 -->
        <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
          <h3 class="text-xl font-bold text-white mb-5 flex items-center">
            <i class="ri-trending-up-line mr-3 text-blue-400"></i>
            {{ t('analysis.trend_analysis') }}
          </h3>

          <!-- 概率显示 -->
          <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="text-center p-4 bg-green-500/10 border border-green-500/30 rounded-xl">
              <div class="text-2xl font-bold text-green-400">
                {{ formatProbability(analysisData.trend_analysis?.probabilities?.up) }}%
              </div>
              <div class="text-sm text-green-300 mt-1">{{ t('analysis.up_trend') }}</div>
            </div>
            <div class="text-center p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-xl">
              <div class="text-2xl font-bold text-yellow-400">
                {{ formatProbability(analysisData.trend_analysis?.probabilities?.sideways) }}%
              </div>
              <div class="text-sm text-yellow-300 mt-1">{{ t('analysis.sideways_trend') }}</div>
            </div>
            <div class="text-center p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
              <div class="text-2xl font-bold text-red-400">
                {{ formatProbability(analysisData.trend_analysis?.probabilities?.down) }}%
              </div>
              <div class="text-sm text-red-300 mt-1">{{ t('analysis.down_trend') }}</div>
            </div>
          </div>

          <!-- 趋势总结 -->
          <div class="bg-slate-700/30 border border-slate-600/50 rounded-xl p-4">
            <p class="text-slate-300 leading-relaxed">{{ analysisData.trend_analysis?.summary }}</p>
          </div>
        </div>

        <!-- 交易建议卡片 -->
        <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
          <h3 class="text-xl font-bold text-white mb-5 flex items-center">
            <i class="ri-lightbulb-line mr-3 text-yellow-400"></i>
            {{ t('analysis.trading_advice') }}
          </h3>

          <div class="space-y-4">
            <!-- 推荐操作 -->
            <div class="flex items-center justify-between p-4 bg-slate-700/30 border border-slate-600/50 rounded-xl">
              <span class="text-slate-400">{{ t('analysis.recommended_action') }}</span>
              <span class="font-semibold text-white">{{ analysisData.trading_advice?.action }}</span>
            </div>

            <!-- 操作理由 -->
            <div class="bg-slate-700/30 border border-slate-600/50 rounded-xl p-4">
              <div class="text-slate-400 text-sm mb-2">{{ t('analysis.reason') }}</div>
              <p class="text-slate-300 leading-relaxed">{{ analysisData.trading_advice?.reason }}</p>
            </div>
          </div>
        </div>

      </div>

      <!-- 底部导航栏 -->
      <nav class="sticky bottom-0 bg-[#0F172A]/95 backdrop-blur-md border-t border-slate-700/50">
        <div class="grid grid-cols-3 h-16">
          <router-link to="/" class="flex flex-col items-center justify-center text-blue-400 border-t-2 border-blue-400">
            <i class="ri-line-chart-line text-xl"></i>
            <span class="text-xs mt-1 font-medium">{{ t('nav.market') }}</span>
          </router-link>
          <router-link to="/points" class="flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors">
            <i class="ri-coin-line text-xl"></i>
            <span class="text-xs mt-1">{{ t('nav.points') }}</span>
          </router-link>
          <router-link to="/profile" class="flex flex-col items-center justify-center text-slate-500 hover:text-slate-300 transition-colors">
            <i class="ri-settings-3-line text-xl"></i>
            <span class="text-xs mt-1">{{ t('nav.settings') }}</span>
          </router-link>
        </div>
      </nav>

      <!-- 刷新加载模态框 -->
      <LoadingModal
        v-if="isRefreshing"
        :visible="isRefreshing"
        type="refresh"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import { ElTooltip, ElMessage } from 'element-plus'
import { useEnhancedI18n } from '@/utils/i18n-helper'

const { t } = useEnhancedI18n()

import { getTechnicalAnalysis, getLatestTechnicalAnalysis } from '@/api'
import type { FormattedTechnicalAnalysisData } from '@/types/technical-analysis'
import ChartSkeleton from '@/components/ChartSkeleton.vue'
import MarketHeader from '@/components/MarketHeader.vue'
import FavoriteButton from '@/components/FavoriteButton.vue'
import LoadingModal from '@/components/LoadingModal.vue'
import TokenNotFoundView from '@/components/TokenNotFoundView.vue'
// @ts-ignore
import { googleTranslate } from '@/utils/translate'

// 响应式数据
const currentMarketType = ref<'crypto' | 'stock' | 'china'>('crypto')
const currentSymbol = ref<string>('')
const analysisData = ref<FormattedTechnicalAnalysisData | null>(null)
const originalAnalysisData = ref<FormattedTechnicalAnalysisData | null>(null) // 保存原始英文数据
const loading = ref(false)
const analysisLoading = ref(false)
const error = ref<string | null>(null)
const isTokenNotFound = ref(false)
const isRefreshing = ref(false)
const showSkeleton = ref(true)
const activePanel = ref<'search' | 'favorites' | 'popular' | null>(null)
const currentLang = ref(localStorage.getItem('language') || 'en-US')

// 搜索相关
const searchQuery = ref('')
const searchResults = ref<any[]>([])
const searchLoading = ref(false)

// 收藏相关
const filteredFavorites = ref<any[]>([])

// 热门资产
const popularTokens = ref([
  { symbol: 'BTC', display: 'BTC' },
  { symbol: 'ETH', display: 'ETH' },
  { symbol: 'SOL', display: 'SOL' },
  { symbol: 'BNB', display: 'BNB' },
  { symbol: 'XRP', display: 'XRP' },
  { symbol: 'ADA', display: 'ADA' },
  { symbol: 'DOGE', display: 'DOGE' },
  { symbol: 'AVAX', display: 'AVAX' }
])

const popularStocks = ref([
  { symbol: 'AAPL', display: 'AAPL' },
  { symbol: 'GOOGL', display: 'GOOGL' },
  { symbol: 'MSFT', display: 'MSFT' },
  { symbol: 'AMZN', display: 'AMZN' },
  { symbol: 'TSLA', display: 'TSLA' },
  { symbol: 'META', display: 'META' },
  { symbol: 'NVDA', display: 'NVDA' },
  { symbol: 'NFLX', display: 'NFLX' }
])

// 当前热门资产计算属性
const currentPopularAssets = computed(() => {
  if (currentMarketType.value === 'crypto') {
    return popularTokens.value
  } else if (currentMarketType.value === 'stock') {
    return popularStocks.value
  } else {
    return []
  }
})

// 刷新限制
const canRefreshReport = ref(true)

// 工具函数
const formatPrice = (price: number | undefined) => {
  if (!price) return '--'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 6
  }).format(price)
}

const formatTime = (timestamp: string | undefined) => {
  if (!timestamp) return '--'
  return new Date(timestamp).toLocaleString()
}

const formatProbability = (value: number | undefined) => {
  if (!value) return '0'
  // 如果值大于1，说明已经是百分比格式，直接返回
  if (value > 1) {
    return Math.round(value).toString()
  }
  // 如果值小于等于1，说明是小数格式，需要乘以100
  return Math.round(value * 100).toString()
}

const getDisplayTitle = () => {
  return currentSymbol.value
}

// 面板切换
const togglePanel = (panel: 'search' | 'favorites' | 'popular') => {
  if (activePanel.value === panel) {
    activePanel.value = null
  } else {
    activePanel.value = panel
  }
}

// 市场类型变更
const handleMarketTypeChange = (newType: 'crypto' | 'stock' | 'china') => {
  currentMarketType.value = newType
  activePanel.value = null

  if (newType === 'china') {
    return
  }

  // 切换到默认资产
  const defaultSymbol = newType === 'crypto' ? 'BTC' : 'AAPL'
  currentSymbol.value = defaultSymbol
  loadAnalysisData(defaultSymbol)
}

// 搜索处理
let searchTimeout: NodeJS.Timeout
const handleSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(async () => {
    if (!searchQuery.value.trim()) {
      searchResults.value = []
      return
    }

    searchLoading.value = true
    try {
      // 模拟搜索延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      // 这里应该调用实际的搜索API
      searchResults.value = []
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      searchLoading.value = false
    }
  }, 300)
}

// 选择搜索结果
const selectSearchResult = (result: any) => {
  currentSymbol.value = result.symbol
  activePanel.value = null
  searchQuery.value = ''
  searchResults.value = []
  loadAnalysisData(result.symbol)
}

// 选择收藏
const selectFavorite = (favorite: any) => {
  currentSymbol.value = favorite.symbol
  activePanel.value = null
  loadAnalysisData(favorite.symbol)
}

// 选择热门资产
const selectPopularAsset = (asset: any) => {
  if (asset.symbol === currentSymbol.value) return
  analysisLoading.value = true
  currentSymbol.value = asset.symbol
  activePanel.value = null
  loadAnalysisData(asset.symbol).finally(() => {
    analysisLoading.value = false
  })
}

// 收藏变更处理
const handleFavoriteChanged = (isFavorite: boolean) => {
  console.log('Favorite changed:', isFavorite)
}

// 分享到Twitter
const shareToTwitter = () => {
  const text = `Check out the technical analysis for ${currentSymbol.value}`
  const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`
  window.open(url, '_blank')
}

// 保存图表图片
const saveChartImage = () => {
  ElMessage.info('Save chart image feature coming soon!')
}

// 翻译分析数据
const translateAnalysisData = async (data: FormattedTechnicalAnalysisData, targetLang: string) => {
  if (!data || targetLang === 'en-US') {
    return data
  }

  try {
    const translatedData = { ...data }

    // 翻译趋势分析总结
    if (data.trend_analysis?.summary) {
      translatedData.trend_analysis = {
        ...data.trend_analysis,
        summary: await googleTranslate(data.trend_analysis.summary, targetLang)
      }
    }

    // 翻译交易建议理由
    if (data.trading_advice?.reason) {
      translatedData.trading_advice = {
        ...data.trading_advice,
        reason: await googleTranslate(data.trading_advice.reason, targetLang)
      }
    }

    // 翻译风险评估详情
    if (data.risk_assessment?.details && Array.isArray(data.risk_assessment.details)) {
      const translatedDetails = await Promise.all(
        data.risk_assessment.details.map(detail => googleTranslate(detail, targetLang))
      )
      translatedData.risk_assessment = {
        ...data.risk_assessment,
        details: translatedDetails
      }
    }

    return translatedData
  } catch (error) {
    console.error('Translation error:', error)
    return data // 翻译失败时返回原数据
  }
}

// 设置语言变更监听器
const setupLanguageChangeListener = () => {
  window.addEventListener('language-changed', async (event) => {
    const newLang = (event as CustomEvent).detail?.language || localStorage.getItem('language') || 'en-US'
    console.log(`[HomeView] 收到语言变更事件: ${newLang}`)
    currentLang.value = newLang

    // 如果有原始数据，重新翻译
    if (originalAnalysisData.value) {
      const translatedData = await translateAnalysisData(originalAnalysisData.value, newLang)
      analysisData.value = translatedData
    }
  })

  window.addEventListener('force-refresh-i18n', async () => {
    const newLang = localStorage.getItem('language') || 'en-US'
    console.log(`[HomeView] 收到强制刷新事件: ${newLang}`)
    currentLang.value = newLang

    // 如果有原始数据，重新翻译
    if (originalAnalysisData.value) {
      const translatedData = await translateAnalysisData(originalAnalysisData.value, newLang)
      analysisData.value = translatedData
    }
  })
}

// 刷新报告
const handleRefreshReport = async () => {
  if (!canRefreshReport.value || isRefreshing.value) return

  isRefreshing.value = true
  canRefreshReport.value = false

  try {
    await loadAnalysisData(currentSymbol.value, true)
    ElMessage.success(t('analysis.refresh_success'))
  } catch (error) {
    console.error('Refresh error:', error)
    ElMessage.error(t('analysis.refresh_error'))
  } finally {
    isRefreshing.value = false
    // 30秒后允许再次刷新
    setTimeout(() => {
      canRefreshReport.value = true
    }, 30000)
  }
}

// 加载分析数据
const loadAnalysisData = async (symbol: string, forceRefresh = false) => {
  if (!symbol) return

  loading.value = true
  error.value = null
  isTokenNotFound.value = false
  showSkeleton.value = true

  try {
    let response
    const marketType = currentMarketType.value === 'china' ? 'stock' : currentMarketType.value as 'crypto' | 'stock'

    if (forceRefresh) {
      response = await getLatestTechnicalAnalysis(symbol, marketType)
    } else {
      response = await getTechnicalAnalysis(symbol, false, marketType)
    }

    if (response && (response as any).status !== 'not_found') {
      analysisData.value = response
      showSkeleton.value = false
    } else {
      isTokenNotFound.value = true
      showSkeleton.value = false
    }
  } catch (err: any) {
    console.error('Load analysis data error:', err)
    if (err.response?.status === 404) {
      isTokenNotFound.value = true
    } else {
      error.value = err.message || 'Failed to load data'
    }
    showSkeleton.value = false
  } finally {
    loading.value = false
  }
}

// 组件挂载
onMounted(async () => {
  console.log('HomeView mounted')

  // 检查登录状态
  const token = localStorage.getItem('token')
  const userInfo = localStorage.getItem('userInfo')
  if (!token || !userInfo) {
    window.location.href = '/login'
    return
  }

  // 设置默认symbol并加载数据
  currentSymbol.value = 'BTC'
  await loadAnalysisData('BTC')
})

// 监听数据变化
watch(analysisData, (newData) => {
  if (newData) {
    showSkeleton.value = false
  }
})

watch([isTokenNotFound, error], ([tokenNotFound, errorState]) => {
  if (tokenNotFound || errorState) {
    showSkeleton.value = false
  }
})
</script>
