{"name": "cooltrade-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:extension": "vite build && npm run post-build", "post-build": "node scripts/post-build.js", "preview": "vite preview", "package": "npm run build:extension && cd dist && zip -r ../cooltrade-extension.zip .", "package:version": "npm run build:extension && cd dist && zip -r ../cooltrade-extension-v$(node -p \"require('../package.json').version\")-$(date +%Y%m%d-%H%M%S).zip ."}, "dependencies": {"vue": "^3.4.15", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.9.0", "vue-i18n": "^12.0.0-alpha.2", "element-plus": "^2.9.9", "remixicon": "^4.2.0", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vueuse/core": "^13.1.0", "echarts": "^5.6.0", "html2canvas": "^1.4.1", "qrcode": "^1.5.4", "qrcode.js": "^0.0.1", "tippy.js": "^6.3.7"}, "devDependencies": {"@types/node": "^20.11.17", "@types/chrome": "^0.0.317", "@vitejs/plugin-vue": "^5.0.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.40.0", "typescript": "^5.3.3", "vite": "^5.0.12", "vue-tsc": "^1.8.27"}}